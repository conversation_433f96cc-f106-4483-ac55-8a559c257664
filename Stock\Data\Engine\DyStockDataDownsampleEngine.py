import pandas as pd
import numpy as np
from datetime import datetime, time
from collections import OrderedDict

from EventEngine.DyEvent import *
from DyCommon.DyCommon import *
from ..DyStockDataCommon import *
from ...Common.DyStockCommon import *


class DyStockDataDownsampleEngine(object):
    """
    股票数据降采样引擎
    负责将1分钟数据降采样到指定周期
    """
    
    def __init__(self, eventEngine, mongoDbEngine, gateway, info, registerEvent=True, dataEngine=None):
        self._eventEngine = eventEngine
        self._mongoDbEngine = mongoDbEngine
        self._gateway = gateway
        self._info = info
        self._dataEngine = dataEngine  # 添加数据引擎引用

        self._isStopped = False

        if registerEvent:
            self._registerEvent()
            
    def _registerEvent(self):
        self._eventEngine.register(DyEventType.downsampleMin1Data, self._downsampleMin1DataHandler)
        self._eventEngine.register(DyEventType.stopDownsampleMin1DataReq, self._stopDownsampleMin1DataReqHandler)
        self._eventEngine.register(DyEventType.batchDownsampleMin1Data, self._batchDownsampleMin1DataHandler)
        self._eventEngine.register(DyEventType.getDownsampleStatus, self._getDownsampleStatusHandler)
        self._eventEngine.register(DyEventType.cleanDownsampleData, self._cleanDownsampleDataHandler)

    def _stopDownsampleMin1DataReqHandler(self, event):
        self._isStopped = True

    def _batchDownsampleMin1DataHandler(self, event):
        """
        处理批量降采样请求
        event.data: {'periods': [int, int, ...]}
        """
        self._isStopped = False

        periods = event.data.get('periods')
        if not periods:
            self._info.print('批量降采样周期参数错误', DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        self._info.print(f'开始批量降采样: {periods}', DyLogData.ind)

        # 智能检查和补全基础数据
        if not self._smartCheckAndFixBaseData():
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # 执行批量降采样
        if self.batchDownsample(periods):
            self._info.print(f'批量降采样完成', DyLogData.ind)
            self._eventEngine.put(DyEvent(DyEventType.finish))
        else:
            self._eventEngine.put(DyEvent(DyEventType.fail))

    def _getDownsampleStatusHandler(self, event):
        """
        处理获取降采样状态请求
        """
        try:
            # 获取降采样状态
            status = self.getDownsampleStatus()

            # 打印状态信息
            self._info.print('=' * 60, DyLogData.ind)
            self._info.print('降采样数据状态:', DyLogData.ind)

            for period, info in status.items():
                if info['latest_date']:
                    status_text = '最新' if info['is_up_to_date'] else '需要更新'
                    self._info.print(f'  {period}分钟: {status_text} (最新日期: {info["latest_date"]})', DyLogData.ind)
                else:
                    self._info.print(f'  {period}分钟: 无数据', DyLogData.ind)

            self._info.print('=' * 60, DyLogData.ind)

            # 发送完成事件
            self._eventEngine.put(DyEvent(DyEventType.finish))

        except Exception as ex:
            self._info.print(f'获取降采样状态异常: {ex}', DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))

    def _cleanDownsampleDataHandler(self, event):
        """
        处理清理降采样数据请求
        event.data: {'period': int or None}
        """
        try:
            period = event.data.get('period')

            # 清理数据
            if self.cleanupDownsampleData(period):
                if period:
                    self._info.print(f'清理{period}分钟降采样数据完成', DyLogData.ind)
                else:
                    self._info.print('清理所有降采样数据完成', DyLogData.ind)

                self._eventEngine.put(DyEvent(DyEventType.finish))
            else:
                self._eventEngine.put(DyEvent(DyEventType.fail))

        except Exception as ex:
            self._info.print(f'清理降采样数据异常: {ex}', DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))
        
    def _downsampleMin1DataHandler(self, event):
        """
        处理降采样请求 - 增强版
        event.data: {'period': int}  # 降采样周期（分钟）
        """
        self._isStopped = False

        period = event.data.get('period')
        if not period:
            self._info.print('降采样周期参数错误', DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        self._info.print(f'开始1分钟数据降采样到{period}分钟（智能模式）...', DyLogData.ind)

        # 智能检查和补全基础数据
        if not self._smartCheckAndFixBaseData():
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # 执行降采样
        if self._downsampleData(period):
            self._info.print(f'1分钟数据降采样到{period}分钟完成', DyLogData.ind)
            self._eventEngine.put(DyEvent(DyEventType.finish))
        else:
            self._eventEngine.put(DyEvent(DyEventType.fail))
            
    def _checkAndUpdateMin1Data(self):
        """
        检查1分钟数据是否最新，如果不是则更新
        增强版：更全面的数据完整性检查和自动修复
        """
        # 获取1分钟数据的最新日期
        latestMin1Date = self._mongoDbEngine.getLatestMin1Date()
        if not latestMin1Date:
            self._info.print('数据库中没有1分钟数据，需要先更新1分钟数据', DyLogData.error)
            # 发送自动更新事件
            event = DyEvent(DyEventType.updateStockHistMin1)
            event.data = None  # None表示自动更新
            self._eventEngine.put(event)
            return False

        # 获取当前应该的最新交易日
        now = datetime.now()
        today = now.strftime('%Y-%m-%d')

        # 获取最新的交易日
        tradeDays = self._gateway.getTradeDays(latestMin1Date, today)
        if not tradeDays:
            self._info.print('无法获取交易日数据', DyLogData.error)
            return False

        # 检查是否需要更新
        needUpdate = False
        if self._gateway.isNowAfterTradingTime():
            # 收盘后，检查是否包含今天的数据
            if tradeDays and tradeDays[-1] > latestMin1Date:
                needUpdate = True
                targetDate = tradeDays[-1]
        else:
            # 交易时间内，检查是否包含昨天的数据
            if len(tradeDays) > 1 and tradeDays[-2] > latestMin1Date:
                needUpdate = True
                targetDate = tradeDays[-2]

        # 检查数据完整性
        stock_list = self._mongoDbEngine.getStockCodes()
        if stock_list:
            # 转换为代码列表用于抽样检查
            codes = [stock['code'] for stock in stock_list] if isinstance(stock_list[0], dict) else stock_list

            # 抽样检查股票的数据完整性（增加样本数量）
            sample_size = min(20, len(codes))  # 增加样本数量到20
            sample_codes = codes[:sample_size]
            incomplete_codes = []
            missing_dates = set()

            self._info.print(f'检查1分钟数据完整性（抽样{sample_size}只股票）...', DyLogData.ind)

            # 检查最近5个交易日的数据完整性
            check_dates = tradeDays[-5:] if len(tradeDays) >= 5 else tradeDays

            for code in sample_codes:
                # 尝试获取股票名称
                stock_name = None
                if isinstance(stock_list[0], dict):
                    # 查找对应的股票名称
                    for stock in stock_list:
                        if stock['code'] == code:
                            stock_name = stock['name']
                            break

                # 检查每个日期的数据
                for check_date in check_dates:
                    df = self._mongoDbEngine.getOneCodeMin1(code, check_date, check_date,
                                                           DyStockDataCommon.min1Indicators, stock_name)

                    # 检查数据是否存在且完整
                    if df is None or df.empty:
                        incomplete_codes.append(code)
                        missing_dates.add(check_date)
                    elif len(df) < 240:  # 一天应该有240条1分钟数据
                        incomplete_codes.append(code)
                        missing_dates.add(check_date)

            # 数据完整性问题处理
            if incomplete_codes:
                self._info.print(f'发现1分钟数据不完整：{len(set(incomplete_codes))}/{sample_size}只股票有问题', DyLogData.warning)
                self._info.print(f'问题日期：{", ".join(sorted(missing_dates))}', DyLogData.warning)
                needUpdate = True

        if needUpdate:
            self._info.print(f'1分钟数据需要更新，启动自动更新...', DyLogData.ind)

            # 发送更新事件
            event = DyEvent(DyEventType.updateStockHistMin1)
            event.data = None  # None表示自动更新
            self._eventEngine.put(event)

            self._info.print('请等待1分钟数据更新完成后再执行降采样', DyLogData.ind)
            return False

        self._info.print('1分钟数据检查完成，数据完整且最新', DyLogData.ind)
        return True

    def _smartCheckAndFixBaseData(self):
        """
        智能检查和修复基础数据
        使用智能数据管理器进行全面的数据检查和自动修复
        """
        try:
            # 获取智能数据管理器
            if not hasattr(self, '_dataEngine') or not hasattr(self._dataEngine, 'smartManager'):
                self._info.print('智能数据管理器不可用，使用传统检查方式', DyLogData.warning)
                return self._checkAndUpdateMin1Data()

            smartManager = self._dataEngine.smartManager

            # 获取股票列表
            stock_list = self._mongoDbEngine.getStockCodes()
            if not stock_list:
                self._info.print('无法获取股票代码列表', DyLogData.error)
                return False

            # 统一处理股票列表格式
            if isinstance(stock_list[0], dict):
                codes = [stock['code'] for stock in stock_list]
            else:
                codes = stock_list

            # 获取检查的日期范围（最近10个交易日）
            endDate = self._mongoDbEngine.getDaysLatestTradeDay()
            if not endDate:
                self._info.print('无法获取最新交易日', DyLogData.error)
                return False

            # 获取开始日期（向前推10个交易日）
            tradeDays = self._gateway.getTradeDays('2024-01-01', endDate)
            if not tradeDays or len(tradeDays) < 10:
                self._info.print('无法获取足够的交易日数据', DyLogData.error)
                return False

            startDate = tradeDays[-10]  # 最近10个交易日

            self._info.print(f'智能检查1分钟数据完整性: {startDate} 到 {endDate}', DyLogData.ind)

            # 抽样检查（检查前100只股票）
            sample_codes = codes[:min(100, len(codes))]

            # 使用智能管理器检查数据完整性
            result = smartManager.ensureDataAvailable('min1', sample_codes, startDate, endDate, autoFix=True)

            if result['success']:
                self._info.print('智能数据检查和修复完成', DyLogData.ind)

                # 如果有修复的数据，打印统计信息
                if result['fixed_data']:
                    self._info.print(f'自动修复了{len(result["fixed_data"])}只股票的1分钟数据', DyLogData.ind)

                return True
            else:
                self._info.print('智能数据检查发现问题，但修复失败', DyLogData.error)

                # 打印详细的缺失数据信息
                if result['missing_data']:
                    self._info.print(f'发现{len(result["missing_data"])}只股票存在数据问题', DyLogData.warning)

                    # 打印前5只股票的详细信息
                    for i, (code, issues) in enumerate(list(result['missing_data'].items())[:5]):
                        missing_count = len(issues.get('missing_dates', []))
                        incomplete_count = len(issues.get('incomplete_dates', []))
                        self._info.print(f'  {code}: 缺失{missing_count}天, 不完整{incomplete_count}天', DyLogData.warning)

                # 尝试传统方式检查
                self._info.print('尝试使用传统方式检查1分钟数据...', DyLogData.ind)
                return self._checkAndUpdateMin1Data()

        except Exception as ex:
            self._info.print(f'智能数据检查异常: {ex}', DyLogData.error)
            # 回退到传统检查方式
            self._info.print('回退到传统数据检查方式...', DyLogData.ind)
            return self._checkAndUpdateMin1Data()
        
    def _downsampleData(self, period):
        """
        执行降采样 - 增强版
        @period: 降采样周期（分钟）
        """
        # 验证降采样周期
        if not self._validatePeriod(period):
            return False

        # 获取股票代码列表
        stock_list = self._mongoDbEngine.getStockCodes()
        if not stock_list:
            self._info.print('无法获取股票代码列表', DyLogData.error)
            return False

        # 统一处理股票列表格式
        if isinstance(stock_list[0], dict):
            # 新格式：[{'code': 'xxx', 'name': 'xxx'}, ...]
            stocks = stock_list
        else:
            # 旧格式：['code1', 'code2', ...] - 转换为新格式
            stocks = [{'code': code, 'name': code} for code in stock_list]

        # 获取日期范围
        dateRange = self._getDateRange(period)
        if not dateRange:
            return False

        startDate, endDate, isIncremental = dateRange

        self._info.print(f'{"增量" if isIncremental else "全量"}生成{period}分钟降采样数据', DyLogData.ind)
        self._info.print(f'日期范围: {startDate} 到 {endDate}', DyLogData.ind)
        self._info.print(f'股票数量: {len(stocks)}只', DyLogData.ind)

        # 进度初始化
        progress = DyProgress(self._info)
        progress.init(len(stocks), 100, 5)

        # 统计信息
        stats = {
            'total': len(stocks),
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'data_points': 0
        }

        # 逐个股票处理
        for stock in stocks:
            if self._isStopped:
                self._info.print('降采样被停止', DyLogData.warning)
                return False

            code = stock['code']
            name = stock['name']

            try:
                # 处理单个股票的降采样
                result = self._processSingleStock(code, name, period, startDate, endDate)

                if result['status'] == 'success':
                    stats['success'] += 1
                    stats['data_points'] += result['data_points']
                elif result['status'] == 'failed':
                    stats['failed'] += 1
                    self._info.print(f'✗ {code}:{name} {result["message"]}', DyLogData.warning)
                else:  # skipped
                    stats['skipped'] += 1

            except Exception as ex:
                stats['failed'] += 1
                self._info.print(f'✗ {code}:{name} 降采样异常: {ex}', DyLogData.error)

            progress.update()

        # 打印最终统计
        self._printFinalStats(period, stats)

        # 验证成功率
        success_rate = stats['success'] / stats['total'] if stats['total'] > 0 else 0
        if success_rate < 0.1:  # 成功率低于10%
            self._info.print(f'降采样成功率过低({success_rate:.1%})，可能存在系统性问题', DyLogData.error)
            return False

        return True

    def _validatePeriod(self, period):
        """验证降采样周期"""
        if not isinstance(period, int) or period <= 0:
            self._info.print(f'无效的降采样周期: {period}', DyLogData.error)
            return False

        if period > 240:  # 一天最多240分钟
            self._info.print(f'降采样周期过大: {period}分钟', DyLogData.error)
            return False

        return True

    def _getDateRange(self, period):
        """获取降采样的日期范围"""
        # 获取最新交易日期范围（用于增量更新判断）
        latestDate = self._mongoDbEngine.getLatestDownsampleDate(period)

        if latestDate:
            # 增量更新：从最后日期开始
            startDate = latestDate
            isIncremental = True
        else:
            # 全量更新：获取1分钟数据的最早日期
            startDate = self._mongoDbEngine.getMin1EarliestDate()
            if not startDate:
                self._info.print('无法获取1分钟数据的最早日期', DyLogData.error)
                return None
            isIncremental = False

        # 获取结束日期（最新交易日）
        endDate = self._mongoDbEngine.getDaysLatestTradeDay()
        if not endDate:
            self._info.print('无法获取最新交易日', DyLogData.error)
            return None

        return startDate, endDate, isIncremental

    def _processSingleStock(self, code, name, period, startDate, endDate):
        """处理单个股票的降采样"""
        try:
            # 获取该股票的1分钟数据
            df = self._mongoDbEngine.getOneCodeMin1(code, startDate, endDate,
                                                    DyStockDataCommon.min1Indicators, name)

            if df is None or df.empty:
                return {'status': 'skipped', 'message': '无1分钟数据'}

            # 数据质量检查
            if not self._validateMin1Data(df, code):
                return {'status': 'failed', 'message': '1分钟数据质量检查失败'}

            # 执行降采样
            downsampledDf = self._downsampleMin1(df, period)

            if downsampledDf is None or downsampledDf.empty:
                return {'status': 'failed', 'message': '降采样处理失败（数据为空）'}

            # 降采样数据质量检查
            if not self._validateDownsampledData(downsampledDf, period):
                return {'status': 'failed', 'message': '降采样数据质量检查失败'}

            # 保存降采样数据
            if self._mongoDbEngine.updateDownsampleData(code, period, downsampledDf):
                return {
                    'status': 'success',
                    'data_points': len(downsampledDf),
                    'message': f'降采样成功，生成{len(downsampledDf)}条数据'
                }
            else:
                return {'status': 'failed', 'message': '降采样保存失败'}

        except Exception as ex:
            return {'status': 'failed', 'message': f'处理异常: {ex}'}

    def _validateMin1Data(self, df, code):
        """验证1分钟数据质量"""
        try:
            # 检查必要的列
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                self._info.print(f'{code}: 缺少必要列 {missing_columns}', DyLogData.warning)
                return False

            # 检查数据完整性
            if df.isnull().any().any():
                null_counts = df.isnull().sum()
                null_columns = null_counts[null_counts > 0]
                self._info.print(f'{code}: 存在空值 {dict(null_columns)}', DyLogData.warning)
                return False

            # 检查价格数据的合理性
            if (df['high'] < df['low']).any():
                self._info.print(f'{code}: 存在最高价小于最低价的异常数据', DyLogData.warning)
                return False

            if (df['high'] < df['open']).any() or (df['high'] < df['close']).any():
                self._info.print(f'{code}: 存在最高价小于开盘价或收盘价的异常数据', DyLogData.warning)
                return False

            if (df['low'] > df['open']).any() or (df['low'] > df['close']).any():
                self._info.print(f'{code}: 存在最低价大于开盘价或收盘价的异常数据', DyLogData.warning)
                return False

            return True

        except Exception as ex:
            self._info.print(f'{code}: 数据验证异常 {ex}', DyLogData.error)
            return False

    def _validateDownsampledData(self, df, period):
        """验证降采样数据质量"""
        try:
            # 基本检查
            if df.empty:
                return False

            # 检查时间间隔是否正确
            if len(df) > 1:
                time_diffs = df.index.to_series().diff().dropna()
                expected_interval = pd.Timedelta(minutes=period)

                # 允许一定的时间误差（考虑到交易时间的特殊性）
                valid_intervals = time_diffs.apply(
                    lambda x: abs(x.total_seconds() - expected_interval.total_seconds()) <= 60
                )

                if not valid_intervals.all():
                    invalid_count = (~valid_intervals).sum()
                    self._info.print(f'降采样数据时间间隔异常: {invalid_count}/{len(valid_intervals)}条', DyLogData.warning)

            return True

        except Exception as ex:
            self._info.print(f'降采样数据验证异常: {ex}', DyLogData.error)
            return False

    def _printFinalStats(self, period, stats):
        """打印最终统计信息"""
        self._info.print('=' * 60, DyLogData.ind)
        self._info.print(f'{period}分钟降采样统计结果:', DyLogData.ind)
        self._info.print(f'  总计股票: {stats["total"]}只', DyLogData.ind)
        self._info.print(f'  成功处理: {stats["success"]}只', DyLogData.ind)
        self._info.print(f'  处理失败: {stats["failed"]}只', DyLogData.ind)
        self._info.print(f'  跳过处理: {stats["skipped"]}只', DyLogData.ind)
        self._info.print(f'  生成数据点: {stats["data_points"]}条', DyLogData.ind)

        success_rate = stats['success'] / stats['total'] if stats['total'] > 0 else 0
        self._info.print(f'  成功率: {success_rate:.1%}', DyLogData.ind)
        self._info.print('=' * 60, DyLogData.ind)
        
    def _downsampleMin1(self, df, period):
        """
        对1分钟数据进行降采样
        @df: 1分钟数据DataFrame
        @period: 目标周期（分钟）
        @return: 降采样后的DataFrame
        """
        try:
            # 创建重采样规则字符串
            rule = f'{period}T'  # T表示分钟
            
            # 重采样聚合规则
            agg_dict = OrderedDict([
                ('open', 'first'),
                ('high', 'max'),
                ('low', 'min'),
                ('close', 'last'),
                ('volume', 'sum'),
                ('amt', 'sum'),
                ('turn', 'sum'),  # 换手率累加
                ('adjfactor', 'last')  # 复权因子取最后值
            ])
            
            # 按交易日分组处理，避免跨交易日
            result_dfs = []
            for date, group in df.groupby(df.index.date):
                # 设置交易时间范围
                morning_start = time(9, 30)
                morning_end = time(11, 30)
                afternoon_start = time(13, 0)
                afternoon_end = time(15, 0)
                
                # 分别处理上午和下午的数据
                morning_data = group.between_time(morning_start, morning_end)
                afternoon_data = group.between_time(afternoon_start, afternoon_end)
                
                # 重采样
                if not morning_data.empty:
                    morning_resampled = morning_data.resample(rule, closed='left', 
                                                             label='left').agg(agg_dict)
                    # 过滤掉全NaN的行
                    morning_resampled = morning_resampled.dropna(how='all')
                    result_dfs.append(morning_resampled)
                    
                if not afternoon_data.empty:
                    afternoon_resampled = afternoon_data.resample(rule, closed='left', 
                                                                 label='left').agg(agg_dict)
                    # 过滤掉全NaN的行
                    afternoon_resampled = afternoon_resampled.dropna(how='all')
                    result_dfs.append(afternoon_resampled)
                    
            # 合并所有结果
            if result_dfs:
                result = pd.concat(result_dfs)
                # 确保时间索引正确
                result.index.name = 'datetime'
                return result
            else:
                return None
                
        except Exception as ex:
            self._info.print(f'降采样处理异常: {ex}', DyLogData.error)
            return None

    def batchDownsample(self, periods):
        """
        批量降采样多个周期
        @periods: 降采样周期列表，如[3, 5, 10, 15, 30, 60]
        @return: bool
        """
        if not periods:
            self._info.print('降采样周期列表为空', DyLogData.error)
            return False

        self._info.print(f'开始批量降采样，周期: {periods}', DyLogData.ind)

        success_periods = []
        failed_periods = []

        for period in periods:
            if self._isStopped:
                self._info.print('批量降采样被停止', DyLogData.warning)
                break

            self._info.print(f'开始处理{period}分钟降采样...', DyLogData.ind)

            try:
                if self._downsampleData(period):
                    success_periods.append(period)
                    self._info.print(f'✓ {period}分钟降采样完成', DyLogData.ind)
                else:
                    failed_periods.append(period)
                    self._info.print(f'✗ {period}分钟降采样失败', DyLogData.error)
            except Exception as ex:
                failed_periods.append(period)
                self._info.print(f'✗ {period}分钟降采样异常: {ex}', DyLogData.error)

        # 打印批量处理结果
        self._info.print('=' * 60, DyLogData.ind)
        self._info.print('批量降采样结果:', DyLogData.ind)
        self._info.print(f'  成功周期: {success_periods}', DyLogData.ind)
        self._info.print(f'  失败周期: {failed_periods}', DyLogData.ind)
        self._info.print(f'  成功率: {len(success_periods)}/{len(periods)} ({len(success_periods)/len(periods):.1%})', DyLogData.ind)
        self._info.print('=' * 60, DyLogData.ind)

        return len(failed_periods) == 0

    def getDownsampleStatus(self):
        """
        获取降采样状态信息
        @return: dict 包含各周期的状态信息
        """
        common_periods = [3, 5, 10, 15, 30, 60]
        status = {}

        for period in common_periods:
            try:
                latest_date = self._mongoDbEngine.getLatestDownsampleDate(period)
                min1_latest = self._mongoDbEngine.getLatestMin1Date()

                status[period] = {
                    'latest_date': latest_date,
                    'is_up_to_date': latest_date == min1_latest if latest_date and min1_latest else False,
                    'needs_update': latest_date != min1_latest if latest_date and min1_latest else True
                }
            except Exception as ex:
                status[period] = {
                    'latest_date': None,
                    'is_up_to_date': False,
                    'needs_update': True,
                    'error': str(ex)
                }

        return status

    def cleanupDownsampleData(self, period=None):
        """
        清理降采样数据
        @param period: 指定周期，None表示清理所有周期
        @return: bool
        """
        try:
            db = self._client[self.stockDownsampleDbXTquant]

            if period:
                # 清理指定周期的数据
                collections = db.list_collection_names()
                prefix = f"min{period}_"
                target_collections = [c for c in collections if c.startswith(prefix)]

                for collection_name in target_collections:
                    db.drop_collection(collection_name)

                self._info.print(f'清理{period}分钟降采样数据完成，删除{len(target_collections)}个集合', DyLogData.ind)
            else:
                # 清理所有降采样数据
                collections = db.list_collection_names()
                for collection_name in collections:
                    if collection_name.startswith('min') and '_' in collection_name:
                        db.drop_collection(collection_name)

                self._info.print(f'清理所有降采样数据完成，删除{len(collections)}个集合', DyLogData.ind)

            return True

        except Exception as ex:
            self._info.print(f'清理降采样数据异常: {ex}', DyLogData.error)
            return False