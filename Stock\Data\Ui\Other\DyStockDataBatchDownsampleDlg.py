from PyQt5.QtWidgets import *
from PyQt5.QtCore import *


class DyStockDataBatchDownsampleDlg(QDialog):
    """批量降采样对话框"""
    
    def __init__(self, data, parent=None):
        super().__init__(parent)
        
        self._data = data
        self._initUi()

    def _initUi(self):
        self.setWindowTitle('批量降采样')
        self.setWindowModality(Qt.WindowModal)
        
        # 控件
        periodLabel = QLabel('选择降采样周期:')
        
        # 创建复选框组
        self._checkBoxes = {}
        periods = [3, 5, 10, 15, 30, 60]
        
        # 全选/全不选按钮
        selectAllButton = QPushButton('全选')
        selectNoneButton = QPushButton('全不选')
        selectAllButton.clicked.connect(self._selectAll)
        selectNoneButton.clicked.connect(self._selectNone)
        
        # 周期复选框
        checkBoxLayout = QGridLayout()
        for i, period in enumerate(periods):
            checkbox = QCheckBox(f'{period}分钟')
            checkbox.setChecked(True)  # 默认全选
            self._checkBoxes[period] = checkbox
            
            row = i // 3
            col = i % 3
            checkBoxLayout.addWidget(checkbox, row, col)
        
        # 按钮
        cancelPushButton = QPushButton('取消')
        okPushButton = QPushButton('确认')
        cancelPushButton.clicked.connect(self._cancel)
        okPushButton.clicked.connect(self._ok)
        
        # 布局
        mainLayout = QVBoxLayout()
        
        # 标签
        mainLayout.addWidget(periodLabel)
        
        # 全选/全不选按钮
        buttonLayout = QHBoxLayout()
        buttonLayout.addWidget(selectAllButton)
        buttonLayout.addWidget(selectNoneButton)
        buttonLayout.addStretch()
        mainLayout.addLayout(buttonLayout)
        
        # 复选框
        mainLayout.addLayout(checkBoxLayout)
        
        # 确认/取消按钮
        confirmLayout = QHBoxLayout()
        confirmLayout.addWidget(cancelPushButton)
        confirmLayout.addWidget(okPushButton)
        mainLayout.addLayout(confirmLayout)
        
        self.setLayout(mainLayout)
        self.resize(400, 250)

    def _selectAll(self):
        """全选"""
        for checkbox in self._checkBoxes.values():
            checkbox.setChecked(True)
    
    def _selectNone(self):
        """全不选"""
        for checkbox in self._checkBoxes.values():
            checkbox.setChecked(False)

    def _ok(self):
        """确认"""
        selected_periods = []
        for period, checkbox in self._checkBoxes.items():
            if checkbox.isChecked():
                selected_periods.append(period)
        
        if not selected_periods:
            QMessageBox.warning(self, '错误', '请至少选择一个降采样周期')
            return
        
        self._data['periods'] = selected_periods
        self.accept()

    def _cancel(self):
        """取消"""
        self.reject()
