# 昨日涨停选股策略说明

## 📋 策略概述

**策略名称：** DySS_YesterdayLimitUpStrategy  
**策略中文名：** 昨日涨停选股策略  
**策略文件：** `Stock/Select/Strategy/EasyScript/DySS_YesterdayLimitUpStrategy.py`

## 🎯 策略逻辑

基于通达信公式：**20天内无涨停，昨天涨停**

### 核心条件

1. **涨停定义：** 涨幅 >= 9.8%（考虑四舍五入）
2. **昨日涨停：** 上一个交易日涨停
3. **20天内唯一：** 过去20天内只有昨天1次涨停
4. **价格过滤：** 股价 >= 3元，避免低价股
5. **成交量过滤：** 成交量 > 5日均量，确保有资金关注

### 通达信公式对照

```
{通达信公式}
ZT:=(C-REF(C,1))/REF(C,1)*100>=9.8;    {定义涨停条件}
LASTZT:=BARSLAST(ZT);                   {计算上一次涨停到当前的周期数}
CON1:=LASTZT==1;                        {条件1：上一次涨停是昨天}
CON2:=COUNT(ZT,20)==1;                  {条件2：20天内只有1次涨停}
CON1 AND CON2;                          {输出结果}
```

```
{DevilYuan脚本 - 优化版}
# 涨停条件定义（涨幅>=9.8%）
ZT:=(C-REF(C,1))/REF(C,1)*100>=9.8;

# 条件1：直接判断昨天是否涨停（优化：使用REF直接回溯）
CON1:=REF(ZT,1);

# 条件2：20天内只有1次涨停
CON2:=COUNT(ZT,20)==1;

# 附加过滤条件
PRICE_FILTER:=C>=3;                     # 排除价格过低的股票
VOL_FILTER:=V>MA(V,5);                  # 排除成交量过小的股票

# 最终选股条件
SELECT_STOCK:=CON1 AND CON2 AND PRICE_FILTER AND VOL_FILTER;
```

## 🚀 脚本优化说明

### 优化内容
基于用户建议，对原始脚本进行了优化：

**优化前（使用BARSLAST）：**
```
ZT:=(C-REF(C,1))/REF(C,1)*100>=9.8;
LASTZT:=BARSLAST(ZT);
CON1:=LASTZT==1;
```

**优化后（直接使用REF回溯）：**
```
ZT:=(C-REF(C,1))/REF(C,1)*100>=9.8;
CON1:=REF(ZT,1);
```

### 优化效果
1. **代码更简洁：** 减少了一个中间计算步骤（LASTZT变量）
2. **逻辑更直观：** 直接判断昨天是否涨停，更容易理解
3. **性能更好：** 避免了BARSLAST的复杂计算，执行效率提升
4. **维护性更强：** 代码更简单，更容易维护和调试

## 🔧 技术指标说明

### 新增函数

为了支持这个策略，在脚本解析器中新增了两个重要函数：

#### 1. BARSLAST(condition)
- **功能：** 计算上一次满足条件到当前的周期数
- **参数：** condition - 条件数组（布尔值）
- **返回：** 距离上次满足条件的周期数
- **示例：** `BARSLAST(ZT)` 返回上次涨停到现在的天数

#### 2. COUNT(condition, n)
- **功能：** 计算n个周期内满足条件的次数
- **参数：** 
  - condition - 条件数组（布尔值）
  - n - 周期数
- **返回：** 满足条件的次数
- **示例：** `COUNT(ZT,20)` 返回20天内涨停的次数

## 📊 策略参数

| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| 基准日期 | 当前日期 | 选股的基准日期 |
| 数据周期 | 30天 | 需要加载的历史数据天数 |
| 指标脚本 | 预设脚本 | 选股逻辑脚本 |
| 选股说明 | 策略说明 | 详细的策略说明文档 |
| 风险控制 | 风险提示 | 风险控制建议 |

## 🎯 适用场景

### 最佳使用环境
- **强势市场：** 在牛市或强势震荡市中效果更佳
- **突破行情：** 适合捕捉突破性涨停机会
- **短线交易：** 适合短线投资者使用

### 策略优势
1. **突破性强：** 捕捉20天内首次涨停的突破信号
2. **过滤充分：** 排除低价股和成交量不足的股票
3. **逻辑清晰：** 基于经典的通达信公式，逻辑简单明了
4. **风险可控：** 有明确的止损和风险控制建议

## ⚠️ 风险提示

### 主要风险
1. **回调风险：** 涨停后可能面临获利回吐压力
2. **市场风险：** 在弱势市场中成功率可能降低
3. **流动性风险：** 部分股票可能存在流动性不足问题

### 风险控制建议
1. **仓位控制：** 单只股票不超过总资金的5%
2. **止损设置：** 跌破昨日最低价止损
3. **止盈设置：** 获利10-20%考虑减仓
4. **市场环境：** 在强势市场中使用效果更佳
5. **避免追高：** 开盘涨幅过大时谨慎介入

## 🚀 使用方法

### 1. 在DevilYuan中使用

1. 打开DevilYuan主界面
2. 选择"选股" -> "策略选股"
3. 在策略列表中找到"昨日涨停选股策略"
4. 设置基准日期和其他参数
5. 点击"开始选股"

### 2. 参数调整

可以根据需要调整以下参数：
- **数据周期：** 如果需要更长的历史数据分析，可以增加到60天
- **价格过滤：** 可以调整最低价格要求（默认3元）
- **成交量过滤：** 可以调整成交量倍数要求

### 3. 结果分析

选股结果包含以下信息：
- 股票代码和名称
- 当前价格
- 昨日涨幅
- 量比（当前成交量/5日均量）
- 20日涨停次数
- 选股状态

## 📈 策略优化建议

### 短期优化
1. **动态阈值：** 根据市场环境动态调整涨停阈值
2. **行业过滤：** 增加行业轮动分析
3. **技术指标：** 结合其他技术指标进行二次过滤

### 长期优化
1. **机器学习：** 使用机器学习优化参数
2. **多因子模型：** 结合基本面因子
3. **风险模型：** 建立更完善的风险评估模型

## 📝 更新日志

### v1.0 (2024-12-19)
- ✅ 实现基础的昨日涨停选股逻辑
- ✅ 新增BARSLAST和COUNT函数支持
- ✅ 添加价格和成交量过滤条件
- ✅ 完善错误处理和调试功能
- ✅ 通过完整测试验证

## 🔗 相关文档

- [脚本语言选股功能完善总结报告](脚本语言选股完善总结报告.md)
- [MongoDB保存datetime错误修复报告](MongoDB保存datetime错误修复报告.md)
- [旧版解析器清理完成报告](旧版解析器清理完成报告.md)

---

**策略开发：** 基于用户提供的通达信公式  
**技术实现：** DevilYuan脚本语言选股框架  
**测试状态：** ✅ 完全通过  
**推荐指数：** ⭐⭐⭐⭐⭐
