"""
智能数据管理器
自动检测和补全缺失的基础数据和降采样数据
确保选股、回测、交易等操作的数据完整性
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from .Common.DyStockDataCommonEngine import *
from datetime import datetime, timedelta
import pandas as pd


class DyStockDataSmartManager(object):
    """智能数据管理器"""
    
    def __init__(self, dataEngine):
        """
        @param dataEngine: DyStockDataEngine实例
        """
        self._dataEngine = dataEngine
        self._mongoDbEngine = dataEngine.mongoDbEngine
        self._info = dataEngine.info
        self._eventEngine = dataEngine.eventEngine
        self._gateway = dataEngine.gateway
        
        # 数据类型映射
        self._engineMap = {
            'days': dataEngine.daysEngine,
            'min1': dataEngine.min1Engine,
            'min5': dataEngine.min5Engine,
            'financial': dataEngine.financialTableEngine
        }
        
        # 降采样引擎
        self._downsampleEngine = dataEngine.downsampleEngine
        
        # 数据完整性检查配置
        self._integrityConfig = {
            'min1_min_records_per_day': 240,  # 1分钟数据每天最少记录数
            'min5_min_records_per_day': 48,   # 5分钟数据每天最少记录数
            'days_required_fields': ['open', 'high', 'low', 'close', 'volume'],
            'min_required_fields': ['open', 'high', 'low', 'close', 'volume'],
            'max_missing_days': 5,  # 最多允许缺失的交易日数
        }
    
    def ensureDataAvailable(self, dataType, codes, startDate, endDate, autoFix=True):
        """
        确保数据可用，自动检测和补全缺失数据
        @param dataType: 数据类型 ('days', 'min1', 'min5', 'financial')
        @param codes: 股票代码列表或单个代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param autoFix: 是否自动修复缺失数据
        @return: dict {'success': bool, 'missing_data': dict, 'fixed_data': dict}
        """
        try:
            self._info.print(f'检查{dataType}数据完整性: {startDate} 到 {endDate}', DyLogData.ind)
            
            # 统一处理codes格式
            if isinstance(codes, str):
                codes = [codes]
            
            # 检查数据完整性
            missing_data = self._checkDataIntegrity(dataType, codes, startDate, endDate)
            
            if not missing_data:
                self._info.print(f'{dataType}数据完整，无需补全', DyLogData.ind)
                return {'success': True, 'missing_data': {}, 'fixed_data': {}}
            
            self._info.print(f'发现{dataType}数据缺失: {len(missing_data)}只股票', DyLogData.warning)
            
            if not autoFix:
                return {'success': False, 'missing_data': missing_data, 'fixed_data': {}}
            
            # 自动修复缺失数据
            fixed_data = self._autoFixMissingData(dataType, missing_data, startDate, endDate)
            
            # 检查修复结果
            success = len(fixed_data) > 0
            
            return {
                'success': success,
                'missing_data': missing_data,
                'fixed_data': fixed_data
            }
            
        except Exception as ex:
            self._info.print(f'数据完整性检查异常: {ex}', DyLogData.error)
            return {'success': False, 'missing_data': {}, 'fixed_data': {}}
    
    def _checkDataIntegrity(self, dataType, codes, startDate, endDate):
        """
        检查数据完整性
        @return: dict {code: {'missing_dates': [date], 'incomplete_dates': [date]}}
        """
        missing_data = {}
        
        # 获取交易日列表
        tradeDays = self._gateway.getTradeDays(startDate, endDate)
        if not tradeDays:
            self._info.print('无法获取交易日数据', DyLogData.error)
            return missing_data
        
        for code in codes:
            code_issues = {'missing_dates': [], 'incomplete_dates': []}
            
            for date in tradeDays:
                # 检查数据是否存在
                if not self._isDataExisting(dataType, code, date):
                    code_issues['missing_dates'].append(date)
                    continue
                
                # 检查数据是否完整
                if not self._isDataComplete(dataType, code, date):
                    code_issues['incomplete_dates'].append(date)
            
            # 如果有问题则记录
            if code_issues['missing_dates'] or code_issues['incomplete_dates']:
                missing_data[code] = code_issues
        
        return missing_data
    
    def _isDataExisting(self, dataType, code, date):
        """检查指定日期的数据是否存在"""
        try:
            if dataType == 'days':
                df = self._mongoDbEngine.getOneCodeDays(code, date, date, ['close'])
            elif dataType == 'min1':
                df = self._mongoDbEngine.getOneCodeMin1(code, date, date, ['close'])
            elif dataType == 'min5':
                df = self._mongoDbEngine.getOneCodeMin5(code, date, date, ['close'])
            elif dataType == 'financial':
                df = self._mongoDbEngine.getOneCodeFinancialTable(code, date, date, ['close'])
            else:
                return False
            
            return df is not None and not df.empty
            
        except Exception:
            return False
    
    def _isDataComplete(self, dataType, code, date):
        """检查指定日期的数据是否完整"""
        try:
            # 获取数据
            if dataType == 'days':
                df = self._mongoDbEngine.getOneCodeDays(code, date, date, 
                                                       self._integrityConfig['days_required_fields'])
                # 日线数据只需要有记录即可
                return df is not None and not df.empty and len(df) > 0
                
            elif dataType in ['min1', 'min5']:
                df = self._mongoDbEngine.getOneCodeMin1(code, date, date, 
                                                       self._integrityConfig['min_required_fields']) if dataType == 'min1' else \
                     self._mongoDbEngine.getOneCodeMin5(code, date, date, 
                                                       self._integrityConfig['min_required_fields'])
                
                if df is None or df.empty:
                    return False
                
                # 检查记录数是否足够
                min_records = self._integrityConfig[f'{dataType}_min_records_per_day']
                return len(df) >= min_records * 0.8  # 允许20%的容错
                
            elif dataType == 'financial':
                df = self._mongoDbEngine.getOneCodeFinancialTable(code, date, date, ['close'])
                return df is not None and not df.empty
            
            return False
            
        except Exception:
            return False
    
    def _autoFixMissingData(self, dataType, missing_data, startDate, endDate):
        """
        自动修复缺失数据
        @param missing_data: 缺失数据信息
        @return: dict 修复结果
        """
        fixed_data = {}
        
        self._info.print(f'开始自动修复{dataType}数据...', DyLogData.ind)
        
        for code, issues in missing_data.items():
            try:
                # 修复基础数据
                if dataType in ['days', 'min1', 'financial']:
                    if self._fixBaseData(dataType, code, issues, startDate, endDate):
                        fixed_data[code] = {'base_data': True}
                
                # 修复降采样数据
                elif dataType in ['min5']:
                    if self._fixDownsampleData(dataType, code, issues, startDate, endDate):
                        fixed_data[code] = {'downsample_data': True}
                
            except Exception as ex:
                self._info.print(f'修复{code}的{dataType}数据失败: {ex}', DyLogData.error)
        
        self._info.print(f'{dataType}数据修复完成: {len(fixed_data)}/{len(missing_data)}只股票', DyLogData.ind)
        return fixed_data
    
    def _fixBaseData(self, dataType, code, issues, startDate, endDate):
        """修复基础数据（日线、1分钟、财务）"""
        try:
            self._info.print(f'修复{code}的{dataType}基础数据...', DyLogData.ind)
            
            # 确定需要更新的日期范围
            all_problem_dates = issues['missing_dates'] + issues['incomplete_dates']
            if not all_problem_dates:
                return True
            
            # 扩展日期范围以确保完整性
            fix_start = min(all_problem_dates)
            fix_end = max(all_problem_dates)
            
            # 触发数据更新
            if dataType == 'days':
                return self._triggerDaysUpdate(code, fix_start, fix_end)
            elif dataType == 'min1':
                return self._triggerMin1Update(code, fix_start, fix_end)
            elif dataType == 'financial':
                return self._triggerFinancialUpdate(code, fix_start, fix_end)
            
            return False
            
        except Exception as ex:
            self._info.print(f'修复{code}基础数据异常: {ex}', DyLogData.error)
            return False
    
    def _fixDownsampleData(self, dataType, code, issues, startDate, endDate):
        """修复降采样数据"""
        try:
            self._info.print(f'修复{code}的{dataType}降采样数据...', DyLogData.ind)
            
            # 首先确保1分钟数据完整
            min1_result = self.ensureDataAvailable('min1', [code], startDate, endDate, autoFix=True)
            if not min1_result['success']:
                self._info.print(f'{code}的1分钟数据修复失败，无法生成降采样数据', DyLogData.error)
                return False
            
            # 获取降采样周期
            period = self._getDownsamplePeriod(dataType)
            if not period:
                return False
            
            # 执行降采样
            return self._triggerDownsample(code, period, startDate, endDate)
            
        except Exception as ex:
            self._info.print(f'修复{code}降采样数据异常: {ex}', DyLogData.error)
            return False
    
    def _getDownsamplePeriod(self, dataType):
        """获取降采样周期"""
        period_map = {
            'min5': 5,
            'min10': 10,
            'min15': 15,
            'min30': 30,
            'min60': 60
        }
        return period_map.get(dataType)
    
    def _triggerDaysUpdate(self, code, startDate, endDate):
        """触发日线数据更新"""
        try:
            # 使用数据引擎的更新方法
            engine = self._engineMap['days']
            if hasattr(engine, '_update'):
                return engine._update(startDate, endDate, 
                                    DyStockDataCommon.dayIndicators, 
                                    [code], isForced=True)
            return False
        except Exception as ex:
            self._info.print(f'触发日线数据更新异常: {ex}', DyLogData.error)
            return False
    
    def _triggerMin1Update(self, code, startDate, endDate):
        """触发1分钟数据更新"""
        try:
            # 使用数据引擎的更新方法
            engine = self._engineMap['min1']
            if hasattr(engine, '_update'):
                return engine._update(startDate, endDate, 
                                    DyStockDataCommon.min1Indicators, 
                                    [code], isForced=True)
            return False
        except Exception as ex:
            self._info.print(f'触发1分钟数据更新异常: {ex}', DyLogData.error)
            return False
    
    def _triggerFinancialUpdate(self, code, startDate, endDate):
        """触发财务数据更新"""
        try:
            # 使用数据引擎的更新方法
            engine = self._engineMap['financial']
            if hasattr(engine, '_update'):
                return engine._update(startDate, endDate, 
                                    DyStockDataCommon.financialTableIndicators, 
                                    [code], isForced=True)
            return False
        except Exception as ex:
            self._info.print(f'触发财务数据更新异常: {ex}', DyLogData.error)
            return False
    
    def _triggerDownsample(self, code, period, startDate, endDate):
        """触发降采样数据生成"""
        try:
            # 获取1分钟数据
            df = self._mongoDbEngine.getOneCodeMin1(code, startDate, endDate, 
                                                   DyStockDataCommon.min1Indicators)
            if df is None or df.empty:
                return False
            
            # 执行降采样
            downsampledDf = self._downsampleEngine._downsampleMin1(df, period)
            if downsampledDf is None or downsampledDf.empty:
                return False
            
            # 保存降采样数据
            return self._mongoDbEngine.updateDownsampleData(code, period, downsampledDf)
            
        except Exception as ex:
            self._info.print(f'触发降采样异常: {ex}', DyLogData.error)
            return False
    
    def preCheckDataForStrategy(self, dataType, codes, startDate, endDate):
        """
        为策略运行预检查数据
        @return: bool 数据是否准备就绪
        """
        result = self.ensureDataAvailable(dataType, codes, startDate, endDate, autoFix=True)
        
        if result['success']:
            self._info.print(f'策略数据预检查通过: {dataType}', DyLogData.ind)
            return True
        else:
            self._info.print(f'策略数据预检查失败: {dataType}', DyLogData.error)
            return False
    
    def getDataIntegrityReport(self, dataType, codes, startDate, endDate):
        """
        获取数据完整性报告
        @return: dict 详细的完整性报告
        """
        missing_data = self._checkDataIntegrity(dataType, codes, startDate, endDate)
        
        total_codes = len(codes)
        problem_codes = len(missing_data)
        integrity_rate = (total_codes - problem_codes) / total_codes if total_codes > 0 else 0
        
        return {
            'data_type': dataType,
            'date_range': f'{startDate} ~ {endDate}',
            'total_codes': total_codes,
            'problem_codes': problem_codes,
            'integrity_rate': integrity_rate,
            'missing_data': missing_data
        }
