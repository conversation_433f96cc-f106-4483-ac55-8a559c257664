from PyQt5.QtWidgets import QDockWidget, QLabel, QSizePolicy, QHBoxLayout, QWidget,QActionGroup,QMenuBar
from PyQt5.QtCore import Qt

from DyCommon.DyScheduler import DyScheduler
from DyCommon.Ui.DyLogWidget import *
from DyCommon.Ui.DyProgressWidget import *
from DyCommon.Ui.DyBasicMainWindow import *
from DyCommon.Ui.DyDateDlg import *
from DyCommon.Ui.DyCodeDateDlg import *

from .Other.DyStockDataStrategyDataPrepareDlg import *
from .Other.DyStockDataHistTicksVerifyDlg import *
from .Other.DyStockDataHistTicksManualUpdateDlg import DyStockDataHistTicksManualUpdateDlg
from .Other.DyStockDataHistMin1ManualUpdateDlg import DyStockDataHistMin1ManualUpdateDlg
from .Other.DyStockDataHistMin5ManualUpdateDlg import DyStockDataHistMin5ManualUpdateDlg
from .Other.DyStockDataHistDaysManualUpdateDlg import DyStockDataHistDaysManualUpdateDlg
from .Other.DyStockDataHistFinancialTableManualUpdateDlg import DyStockDataHistFinancialTableManualUpdateDlg
from .Other.DyStockDataDownsamplePeriodDlg import DyStockDataDownsamplePeriodDlg
from .Other.DyStockDataBatchDownsampleDlg import DyStockDataBatchDownsampleDlg
from .Other.DyStockDataCleanDownsampleDlg import DyStockDataCleanDownsampleDlg

from ..DyStockDataCommon import *
from EventEngine.DyEvent import *
from ..Engine.DyStockDataMainEngine import *
from ...Common.DyStockCommon import *
from ...Select.Ui.Other.DyStockSelectTestedStocksDlg import *


class DyStockDataMainWindow(DyBasicMainWindow):
    name = 'DyStockDataMainWindow'

    def __init__(self, parent=None):
        
        self._mainEngine = DyStockDataMainEngine()

        super().__init__(self._mainEngine.eventEngine, self._mainEngine.info, parent)
        
        self._initUi()

        self._initOthers()

    def _initOthers(self):
        # 禁用DyStockDataCommon模块的日志详细信息记录
        DyStockDataCommon.logDetailsEnabled = False

        # 创建一个调度器实例
        self._scheduler = DyScheduler()
        # 向调度器添加一个定时任务，任务为_timeOneKeyUpdateJob，执行时间为每天18:31:00，执行频率为每周的1, 2, 3, 4, 5, 6, 7（即每天）
        self._scheduler.addJob(self._timeOneKeyUpdateJob, {1, 2, 3, 4, 5, 6, 7}, '18:31:00')

    def _timeOneKeyUpdateJob(self):
        # 检查一键更新按钮的文本是否为'一键更新'
        if self._oneKeyUpdateAction.text() == '一键更新':
            # 打印开始更新的日志信息，使用DyLogData.ind作为日志级别
            self._mainEngine._info.print('开始股票(指数,基金)历史数据定时一键更新...', DyLogData.ind)

            # 调用一键更新方法
            self._oneKeyUpdate()

    def _initUi(self):
        self.setWindowTitle('数据管理')
        # 设置主窗口最小大小
        self.setMinimumSize(800, 600)

        self._initCentral()
        self._initMenu()

        self._loadWindowSettings()
        
    def _initCentral(self):
        # 创建日志和进度dock widget
        widgetLog, dockLog = self._createDock(DyLogWidget, '日志', Qt.TopDockWidgetArea, self._mainEngine.eventEngine)
        widgetProgress, dockProgress = self._createDock(DyProgressWidget, '进度', Qt.BottomDockWidgetArea, self._mainEngine.eventEngine)
        
        # 设置dock widget的特性
        dockLog.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable | QDockWidget.DockWidgetClosable)
        dockProgress.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable | QDockWidget.DockWidgetClosable)
        
        # 创建自定义标题栏
        def createTitleBar(title, fill_char):
            titleBar = QWidget()
            layout = QHBoxLayout(titleBar)
            layout.setContentsMargins(5, 0, 5, 0)
            
            # 添加填充字符
            padding = 20  # 可根据标题长度或窗口宽度调整
            paddedTitle = f"{title[0]}{fill_char * padding}{title[1]}"
            
            label = QLabel(paddedTitle)
            label.setAlignment(Qt.AlignCenter)  # 设置标题居中
            label.setStyleSheet("color: red; font-weight: bold; font-size: 14px;")

            layout.addWidget(label)
            return titleBar

        # 设置自定义标题栏
        dockLog.setTitleBarWidget(createTitleBar('日志', '#'))
        dockProgress.setTitleBarWidget(createTitleBar('进度', '>'))
        
        # 设置dock widget的大小策略
        dockLog.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        dockProgress.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # 添加到主窗口并设置布局
        self.addDockWidget(Qt.TopDockWidgetArea, dockLog)
        self.addDockWidget(Qt.TopDockWidgetArea, dockProgress)
        
        # 分割dock widgets
        self.splitDockWidget(dockLog, dockProgress, Qt.Vertical)

        # 保存dock widget的引用
        self._dockLog = dockLog
        self._dockProgress = dockProgress

        # 设置初始大小
        self._adjustDockSizes()
        self.resizeDocks([dockLog, dockProgress], [300, 100], Qt.Vertical)
        self.resizeEvent = lambda event: self._adjustDockSizes()

        # 添加关闭事件处理
        dockLog.closeEvent = lambda event: self._dockCloseEvent(event, dockLog)
        dockProgress.closeEvent = lambda event: self._dockCloseEvent(event, dockProgress)

        # 监听dock widget的悬浮状态变化
        dockLog.topLevelChanged.connect(lambda: self._dockFloatingChanged(dockLog))
        dockProgress.topLevelChanged.connect(lambda: self._dockFloatingChanged(dockProgress))

    # Central附属函数群<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    def _adjustDockSizes(self):
        """调整dock widgets的大小"""
        if hasattr(self, '_dockLog') and hasattr(self, '_dockProgress'):
            totalHeight = self.height()
            logHeight = int(totalHeight * .8)  # 日志窗体占80%
            progressHeight = int(totalHeight * .2)  # 进度窗体占20%

            # 使用resizeDocks来确保比例
            self.resizeDocks([self._dockLog, self._dockProgress], [logHeight, progressHeight], Qt.Vertical)

    def _dockCloseEvent(self, event, dock):
        """处理dock widget的关闭事件"""
        dock.hide()  # 隐藏而不是关闭
        event.ignore()  # 忽略关闭事件

    def closeEvent(self, event):
        """关闭主窗口时同时关闭所有浮动窗口"""
        # 确保所有dock widget都被关闭
        if hasattr(self, '_dockLog'):
            self._dockLog.close()
        if hasattr(self, '_dockProgress'):
            self._dockProgress.close()
        
        self._mainEngine.exit()
        super().closeEvent(event)
    # Central附属函数群>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

    # 历史分笔
    def _histTicksDataSourceAct(self):
        """处理历史分笔数据源选择"""
        # 获取触发的action
        action = self.sender()
        
        # 设置历史分笔数据源
        dataSource = action.text()
        # 设置默认数据源
        DyStockDataCommon.defaultHistTicksDataSource = dataSource
        
        # 发送更新事件
        event = DyEvent(DyEventType.updateHistTicksDataSource)
        event.data = dataSource
        self._mainEngine.eventEngine.put(event)

    def _histTicksMannualUpdate(self):
        if self._histTicksMannualUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(基金)历史分笔数据手动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistTicksReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始手动更新
            data = {}
            if DyStockDataHistTicksManualUpdateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(基金)历史分笔数据手动更新...', DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histTicksMannualUpdateAction)

                event = DyEvent(DyEventType.updateStockHistTicks)
                event.data = data
                event.data['codes'] = DyStockCommon.getDyStockCodes(event.data['codes'])
                
                self._mainEngine.eventEngine.put(event)
    def _histTicksVerifyAct(self):
        if self._histTicksVerifyAction.text() == '停止':
            self._mainEngine._info.print('停止股票(基金)历史分笔数据校验...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopVerifyStockHistTicksReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始数据校验
            data = {}
            if DyStockDataHistTicksVerifyDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(基金)历史分笔数据校验...', DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histTicksVerifyAction)

                event = DyEvent(DyEventType.verifyStockHistTicks)
                event.data = data
                # 添加indicators
                event.data['indicators'] = DyStockDataCommon.tickIndicators

                self._mainEngine.eventEngine.put(event)

    # def _manualUpdateSectorCodeTableAct(self):
    #     data = {'codes': list(DyStockCommon.sectors)}
    #     if DyCodeDateDlg('板块代码', data, self).exec_():
    #         self._mainEngine._info.print('开始{0}股票板块代码表更新[{1}, {2}]...'.format(data['codes'], data['startDate'], data['endDate']), DyLogData.ind)

    #         # change UI
    #         self._startRunningMutexAction(self._manualUpdateSectorCodeTableAction)

    #         event = DyEvent(DyEventType.updateStockSectorCodes)
    #         event.data = data
    #         event.data['sectorCode'] = data['codes']

    #         self._mainEngine.eventEngine.put(event)
              
    def _histDaysForcedUpdate(self):
        if self._histDaysForcedUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数)历史日线数据强制更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistDaysReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始手动更新
            data = {}
            if DyDateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史日线数据强制更新[{0}, {1}]...'.format(data['startDate'], data['endDate']), DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histDaysForcedUpdateAction)

                event = DyEvent(DyEventType.updateStockHistDays)
                event.data = data
                event.data['indicators'] = DyStockDataCommon.dayIndicators
                event.data['forced'] = True

                self._mainEngine.eventEngine.put(event) 
                 
    def _histDaysMannualUpdate(self):
        if self._histDaysMannualUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数)历史日线数据手动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistDaysReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始手动更新
            data = {}
            if DyStockDataHistDaysManualUpdateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史日线数据手动更新[{0}, {1}]...'.format(data['startDate'], data['endDate']), DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histDaysMannualUpdateAction)
                event = DyEvent(DyEventType.updateStockHistDays)
                event.data = data
                event.data['codes'] = DyStockCommon.getDyStockCodes(event.data['codes'])

                # 是否要更新指数的日线数据
                if event.data['index']:
                    if event.data['codes'] is None:
                        event.data['codes'] = []

                    event.data['codes'].extend(list(DyStockCommon.indexes))
                self._mainEngine.eventEngine.put(event)

    def _histDaysAutoUpdate(self):
        if self._histDaysAutoUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史日线数据自动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistDaysReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始自动更新
            self._mainEngine._info.print('开始股票(指数,基金)历史日线数据自动更新...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._histDaysAutoUpdateAction)

            event = DyEvent(DyEventType.updateStockHistDays)
            event.data = None # None means auto updating

            self._mainEngine.eventEngine.put(event)


    def _histMin1MannualUpdate(self):
        if self._histMin1MannualUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史1分钟数据手动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistMin1Req)
            self._mainEngine.eventEngine.put(event)

        else: # 开始手动更新
            data = {}
            if DyStockDataHistMin1ManualUpdateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史1分钟数据手动更新...', DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histMin1MannualUpdateAction)

                event = DyEvent(DyEventType.updateStockHistMin1)
                event.data = data
                event.data['codes'] = DyStockCommon.getDyStockCodes(event.data['codes'])

                if event.data['index']:
                    if event.data['codes'] is None:
                        event.data['codes'] = []

                    event.data['codes'].extend(list(DyStockCommon.indexes))

                self._mainEngine.eventEngine.put(event)

    def _histMin1AutoUpdate(self):
        """1分钟数据自动更新"""
        if self._histMin1AutoUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史1分钟数据自动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistMin1Req)
            self._mainEngine.eventEngine.put(event)

        else:  # 开始自动更新
            self._mainEngine._info.print('开始股票(指数,基金)历史1分钟数据自动更新...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._histMin1AutoUpdateAction)

            event = DyEvent(DyEventType.updateStockHistMin1)
            event.data = None  # None means auto updating
            self._mainEngine.eventEngine.put(event)

    def _histMin1ForcedUpdate(self):
        """1分钟数据强制更新"""
        if self._histMin1ForcedUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史1分钟数据强制更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistMin1Req)
            self._mainEngine.eventEngine.put(event)

        else:  # 开始强制更新
            data = {}
            if DyDateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史1分钟数据强制更新[{0}, {1}]...'.format(data['startDate'], data['endDate']), DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histMin1ForcedUpdateAction)

                event = DyEvent(DyEventType.updateStockHistMin1)
                event.data = data
                event.data['indicators'] = DyStockDataCommon.min1Indicators
                event.data['forced'] = True
                self._mainEngine.eventEngine.put(event)

    def _histMin5MannualUpdate(self):
        if self._histMin5MannualUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史5分钟数据手动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistMin5Req)
            self._mainEngine.eventEngine.put(event) 

        else: # 开始手动更新
            data = {}
            if DyStockDataHistMin5ManualUpdateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史5分钟数据手动更新...', DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histMin5MannualUpdateAction)

                event = DyEvent(DyEventType.updateStockHistMin5)
                event.data = data
                event.data['codes'] = DyStockCommon.getDyStockCodes(event.data['codes'])

                if event.data['index']:
                    if event.data['codes'] is None:
                        event.data['codes'] = []

                    event.data['codes'].extend(list(DyStockCommon.indexes))

                self._mainEngine.eventEngine.put(event)
    
    def _histMin5AutoUpdate(self):
        """5分钟数据自动更新"""
        if self._histMin5AutoUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史5分钟数据自动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistMin5Req)
            self._mainEngine.eventEngine.put(event)

        else:  # 开始自动更新
            self._mainEngine._info.print('开始股票(指数,基金)历史5分钟数据自动更新...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._histMin5AutoUpdateAction)

            event = DyEvent(DyEventType.updateStockHistMin5)
            event.data = None  # None means auto updating
            self._mainEngine.eventEngine.put(event)

    def _histMin5ForcedUpdate(self):
        """5分钟数据强制更新"""
        if self._histMin5ForcedUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史5分钟数据强制更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistMin5Req)
            self._mainEngine.eventEngine.put(event)

        else:  # 开始强制更新
            data = {}
            if DyDateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史5分钟数据强制更新[{0}, {1}]...'.format(data['startDate'], data['endDate']), DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._histMin5ForcedUpdateAction)

                event = DyEvent(DyEventType.updateStockHistMin5)
                event.data = data
                event.data['indicators'] = DyStockDataCommon.min5Indicators
                event.data['forced'] = True
                self._mainEngine.eventEngine.put(event)

    def _financialtableMannualUpdate(self):
        """财务数据手动更新"""
        if self._financialtableMannualUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史财务数据手动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistFinancialTableReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始手动更新
            data = {}
            if DyStockDataHistFinancialTableManualUpdateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史财务数据手动更新...', DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._financialtableMannualUpdateAction)

                event = DyEvent(DyEventType.updateStockHistFinancialTable)
                event.data = data
                event.data['indicators'] = DyStockDataCommon.financialTableIndicators
                event.data['codes'] = DyStockCommon.getDyStockCodes(event.data['codes'])

                if event.data['index']:
                    if event.data['codes'] is None:
                        event.data['codes'] = []

                    event.data['codes'].extend(list(DyStockCommon.indexes))

                self._mainEngine.eventEngine.put(event)

    def _financialtableAutoUpdate(self):
        """
        自动更新财务数据
        """
        if self._financialtableAutoUpdateAction.text() == '停止':   
            self._mainEngine._info.print('停止股票(指数,基金)历史财务数据自动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistFinancialTableReq)
            self._mainEngine.eventEngine.put(event)
        else:
            self._mainEngine._info.print('开始股票(指数,基金)历史财务数据自动更新...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._financialtableAutoUpdateAction)

            # 创建事件
            event = DyEvent(DyEventType.updateStockHistFinancialTable)
            event.data = None  # None表示更新所有股票
            self._mainEngine.eventEngine.put(event)

    def _financialtableForcedUpdate(self):
        """财务数据强制更新"""
        if self._financialtableForcedUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止股票(指数,基金)历史财务数据强制更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockHistFinancialTableReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始强制更新
            data = {}
            if DyDateDlg(data, self).exec_():
                self._mainEngine._info.print('开始股票(指数,基金)历史财务数据强制更新[{0}, {1}]...'.format(data['startDate'], data['endDate']), DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._financialtableForcedUpdateAction)

                event = DyEvent(DyEventType.updateStockHistFinancialTable)
                event.data = data
                event.data['indicators'] = DyStockDataCommon.financialTableIndicators
                event.data['forced'] = True
                self._mainEngine.eventEngine.put(event)

    def _downsampleMin1Data(self, period):
        """
        降采样1分钟数据到指定周期
        @period: 降采样周期（分钟）
        """
        if hasattr(self, '_downsampleAction') and self._downsampleAction.text() == '停止':
            self._mainEngine._info.print('停止1分钟数据降采样...', DyLogData.ind)
            
            # change UI
            self._stopRunningMutexAction()
            
            event = DyEvent(DyEventType.stopDownsampleMin1DataReq)
            self._mainEngine.eventEngine.put(event)
        else:
            self._mainEngine._info.print(f'开始1分钟数据降采样到{period}分钟...', DyLogData.ind)
            
            # 创建临时action用于UI状态管理
            self._downsampleAction = QAction('停止', self)
            
            # change UI
            self._startRunningMutexAction(self._downsampleAction)
            
            event = DyEvent(DyEventType.downsampleMin1Data)
            event.data = {'period': period}
            self._mainEngine.eventEngine.put(event)
            
    def _downsampleMin1DataCustom(self):
        """
        自定义周期降采样
        """
        data = {}
        if DyStockDataDownsamplePeriodDlg(data, self).exec_():
            period = data.get('period')
            if period:
                self._downsampleMin1Data(period)

    def _batchDownsample(self):
        """
        批量降采样
        """
        data = {}
        if DyStockDataBatchDownsampleDlg(data, self).exec_():
            periods = data.get('periods')
            if periods:
                self._mainEngine._info.print(f'开始批量降采样: {periods}', DyLogData.ind)

                # 创建临时action用于UI状态管理
                self._downsampleAction = QAction('停止', self)

                # change UI
                self._startRunningMutexAction(self._downsampleAction)

                event = DyEvent(DyEventType.batchDownsampleMin1Data)
                event.data = {'periods': periods}
                self._mainEngine.eventEngine.put(event)

    def _showDownsampleStatus(self):
        """
        显示降采样状态
        """
        self._mainEngine._info.print('获取降采样状态...', DyLogData.ind)

        event = DyEvent(DyEventType.getDownsampleStatus)
        self._mainEngine.eventEngine.put(event)

    def _cleanDownsampleData(self):
        """
        清理降采样数据
        """
        data = {}
        if DyStockDataCleanDownsampleDlg(data, self).exec_():
            period = data.get('period')

            # 确认对话框
            if period:
                msg = f'确定要清理{period}分钟的降采样数据吗？'
            else:
                msg = '确定要清理所有降采样数据吗？'

            reply = QMessageBox.question(self, '确认', msg,
                                        QMessageBox.Yes | QMessageBox.No,
                                        QMessageBox.No)

            if reply == QMessageBox.Yes:
                self._mainEngine._info.print(f'开始清理降采样数据: {period if period else "所有周期"}', DyLogData.ind)

                event = DyEvent(DyEventType.cleanDownsampleData)
                event.data = {'period': period}
                self._mainEngine.eventEngine.put(event)

    def _test(self):
        pass

    def _oneKeyUpdate(self):
        if self._oneKeyUpdateAction.text() == '停止':
            # 打印日志信息，表示停止股票(指数,基金)历史数据一键更新
            self._mainEngine._info.print('停止股票(指数,基金)历史数据一键更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            self._mainEngine.eventEngine.put(DyEvent(DyEventType.stopStockOneKeyUpdateReq))

        else: # 开始一键更新
            self._mainEngine._info.print('开始股票(指数,基金)历史数据一键更新...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._oneKeyUpdateAction, 2)

            self._mainEngine.eventEngine.put(DyEvent(DyEventType.stockOneKeyUpdate))

    def _timeOneKeyUpdateAct(self):
        if self._timeOneKeyUpdateAction.text() == '停止定时一键更新':
            self._scheduler.shutdown()
            self._timeOneKeyUpdateAction.setText('启动定时一键更新')
        else:
            self._scheduler.start()
            self._timeOneKeyUpdateAction.setText('停止定时一键更新')

    def _initMenu(self):
        """初始化菜单"""
        # 历史分笔
        self._histTicksMannualUpdateAction = QAction('手动更新...', self)
        self._histTicksMannualUpdateAction.triggered.connect(self._histTicksMannualUpdate)
        self._addMutexAction(self._histTicksMannualUpdateAction)

        self._histTicksVerifyAction = QAction('数据校验...', self)
        self._histTicksVerifyAction.triggered.connect(self._histTicksVerifyAct)
        self._addMutexAction(self._histTicksVerifyAction)

        # 实盘策略准备数据
        self._strategyDataPrepareAction = QAction('生成实盘策略准备数据...', self)
        self._strategyDataPrepareAction.triggered.connect(self._strategyDataPrepare)
        self._addMutexAction(self._strategyDataPrepareAction)

        # 历史日线
        self._histDaysMannualUpdateAction = QAction('手动更新...', self)
        self._histDaysMannualUpdateAction.triggered.connect(self._histDaysMannualUpdate)
        self._addMutexAction(self._histDaysMannualUpdateAction)

        self._histDaysAutoUpdateAction = QAction('自动更新', self)
        self._histDaysAutoUpdateAction.triggered.connect(self._histDaysAutoUpdate)
        self._addMutexAction(self._histDaysAutoUpdateAction)

        self._histDaysForcedUpdateAction = QAction('强制更新...', self)
        self._histDaysForcedUpdateAction.triggered.connect(self._histDaysForcedUpdate)
        self._addMutexAction(self._histDaysForcedUpdateAction)

        self._histDaysCleanAction = QAction('清理...', self)
        # self._histDaysCleanAction.triggered.connect(self._histDaysClean)
        self._addMutexAction(self._histDaysCleanAction)

        # 历史1分钟
        self._histMin1MannualUpdateAction = QAction('手动更新...', self)
        self._histMin1MannualUpdateAction.triggered.connect(self._histMin1MannualUpdate)
        self._addMutexAction(self._histMin1MannualUpdateAction)   

        self._histMin1AutoUpdateAction = QAction('自动更新...', self)
        self._histMin1AutoUpdateAction.triggered.connect(self._histMin1AutoUpdate)
        self._addMutexAction(self._histMin1AutoUpdateAction)

        self._histMin1ForcedUpdateAction = QAction('强制更新...', self)
        self._histMin1ForcedUpdateAction.triggered.connect(self._histMin1ForcedUpdate)
        self._addMutexAction(self._histMin1ForcedUpdateAction)

        self._histMin1CleanAction = QAction('清理...', self)
        # self._histMin1CleanAction.triggered.connect(self._histMin1Clean)
        self._addMutexAction(self._histMin1CleanAction)

        # 历史5分钟
        self._histMin5MannualUpdateAction = QAction('手动更新...', self)
        self._histMin5MannualUpdateAction.triggered.connect(self._histMin5MannualUpdate)
        self._addMutexAction(self._histMin5MannualUpdateAction)

        self._histMin5AutoUpdateAction = QAction('自动更新...', self)
        self._histMin5AutoUpdateAction.triggered.connect(self._histMin5AutoUpdate)
        self._addMutexAction(self._histMin5AutoUpdateAction)

        self._histMin5ForcedUpdateAction = QAction('强制更新...', self)
        self._histMin5ForcedUpdateAction.triggered.connect(self._histMin5ForcedUpdate)
        self._addMutexAction(self._histMin5ForcedUpdateAction)

        self._histMin5CleanAction = QAction('清理...', self)
        # self._histMin5CleanAction.triggered.connect(self._histMin5Clean)
        self._addMutexAction(self._histMin5CleanAction)

        # 历史财务数据
        self._financialtableMannualUpdateAction = QAction('手动更新...', self)
        self._financialtableMannualUpdateAction.triggered.connect(self._financialtableMannualUpdate)
        self._addMutexAction(self._financialtableMannualUpdateAction)

        self._financialtableAutoUpdateAction = QAction('自动更新...', self)
        self._financialtableAutoUpdateAction.triggered.connect(self._financialtableAutoUpdate)
        self._addMutexAction(self._financialtableAutoUpdateAction)

        self._financialtableForcedUpdateAction = QAction('强制更新...', self)
        self._financialtableForcedUpdateAction.triggered.connect(self._financialtableForcedUpdate)
        self._addMutexAction(self._financialtableForcedUpdateAction)

        self._financialtableCleanAction = QAction('清理...', self)
        # self._financialtableCleanAction.triggered.connect(self._financialtableClean)
        self._addMutexAction(self._financialtableCleanAction)

        # 板块数据
        self._sectorDataMannualUpdateAction = QAction('手动更新...', self)
        self._sectorDataMannualUpdateAction.triggered.connect(self._sectorDataMannualUpdate)
        self._addMutexAction(self._sectorDataMannualUpdateAction)

        self._sectorDataAutoUpdateAction = QAction('自动更新', self)
        self._sectorDataAutoUpdateAction.triggered.connect(self._sectorDataAutoUpdate)
        self._addMutexAction(self._sectorDataAutoUpdateAction)

        self._sectorDataForcedUpdateAction = QAction('强制更新', self)
        self._sectorDataForcedUpdateAction.triggered.connect(self._sectorDataForcedUpdate)
        self._addMutexAction(self._sectorDataForcedUpdateAction)

        # 调试股票
        self._testedStocksAction = QAction('调试股票...', self)
        self._testedStocksAction.triggered.connect(self._testedStocks)
        self._testedStocksAction.setCheckable(True)

        # 日志细节
        self._enableLogDetailsAction = QAction('日志细节', self)
        self._enableLogDetailsAction.triggered.connect(self._enableLogDetailsAct)
        self._enableLogDetailsAction.setCheckable(True)
        
        # 更新工具
        self._oneKeyUpdateAction = QAction('一键更新', self)
        self._oneKeyUpdateAction.triggered.connect(self._oneKeyUpdate)
        self._addMutexAction(self._oneKeyUpdateAction)

        self._timeOneKeyUpdateAction = QAction('启动定时一键更新', self)
        self._timeOneKeyUpdateAction.triggered.connect(self._timeOneKeyUpdateAct)
        self._addMutexAction(self._timeOneKeyUpdateAction)

        # 日志
        self._showLogAction = QAction('显示日志', self)
        self._showLogAction.setCheckable(True)
        self._showLogAction.setChecked(True)
        self._showLogAction.triggered.connect(lambda: self._toggleDockWidget(self._dockLog))

        # 进度
        self._showProgressAction = QAction('显示进度', self)
        self._showProgressAction.setCheckable(True)
        self._showProgressAction.setChecked(True)
        self._showProgressAction.triggered.connect(lambda: self._toggleDockWidget(self._dockProgress))

        # 测试
        # testAction = QAction('测试', self)
        # testAction.triggered.connect(self._test)

        # 创建菜单
        menuBar = self.menuBar()

        # 添加日线菜单
        histDaysMenu = menuBar.addMenu('日线数据')
        histDaysMenu.addAction(self._histDaysMannualUpdateAction)
        histDaysMenu.addAction(self._histDaysAutoUpdateAction)
        histDaysMenu.addAction(self._histDaysForcedUpdateAction)
        # histDaysMenu.addAction(self._histDaysCleanAction)
        # histDaysMenu.addAction(self._manualUpdateSectorCodeTableAction)

        # 添加1分钟菜单
        histMin1Menu = menuBar.addMenu('1分钟数据')
        histMin1Menu.addAction(self._histMin1MannualUpdateAction)
        histMin1Menu.addAction(self._histMin1AutoUpdateAction)
        histMin1Menu.addAction(self._histMin1ForcedUpdateAction)
        # histMin1Menu.addAction(self._histMin1CleanAction)

        # 添加5分钟菜单
        histMin5Menu = menuBar.addMenu('5分钟数据')
        histMin5Menu.addAction(self._histMin5MannualUpdateAction)
        histMin5Menu.addAction(self._histMin5AutoUpdateAction)
        histMin5Menu.addAction(self._histMin5ForcedUpdateAction)
        # histMin5Menu.addAction(self._histMin5CleanAction)

        # 添加分笔菜单
        histTicksMenu = menuBar.addMenu('分笔数据')
        histTicksMenu.addAction(self._histTicksMannualUpdateAction)
        histTicksMenu.addAction(self._histTicksVerifyAction)
        # histTicksMenu.addAction(self._histTicksCleanAction)
    
        # 添加基本面菜单
        financialtableMenu = menuBar.addMenu('财务数据')
        financialtableMenu.addAction(self._financialtableMannualUpdateAction)
        financialtableMenu.addAction(self._financialtableAutoUpdateAction)
        financialtableMenu.addAction(self._financialtableForcedUpdateAction)
        # financialtableMenu.addAction(self._financialtableCleanAction)

        # 添加板块数据菜单
        sectorDataMenu = menuBar.addMenu('板块数据')
        sectorDataMenu.addAction(self._sectorDataMannualUpdateAction)
        sectorDataMenu.addAction(self._sectorDataAutoUpdateAction)
        sectorDataMenu.addAction(self._sectorDataForcedUpdateAction)

        # 添加数据库诊断菜单
        databaseMenu = menuBar.addMenu('数据库诊断')
        
        # 健康检查
        healthCheckAction = QAction('健康检查', self)
        healthCheckAction.triggered.connect(self._databaseHealthCheck)
        databaseMenu.addAction(healthCheckAction)
        
        # 数据库优化
        optimizeAction = QAction('优化数据库', self)
        optimizeAction.triggered.connect(self._databaseOptimize)
        databaseMenu.addAction(optimizeAction)
        
        # 数据库修复
        repairAction = QAction('修复数据库', self)
        repairAction.triggered.connect(self._databaseRepair)
        databaseMenu.addAction(repairAction)
        
        # 分隔线
        databaseMenu.addSeparator()
        
        # 集合统计
        statsAction = QAction('集合统计', self)
        statsAction.triggered.connect(self._databaseStats)
        databaseMenu.addAction(statsAction)

        # 添加降采样菜单
        downsampleMenu = menuBar.addMenu('降采样数据')
        
        # 3分钟
        downsample3MinAction = QAction('3分钟', self)
        downsample3MinAction.triggered.connect(lambda: self._downsampleMin1Data(3))
        downsampleMenu.addAction(downsample3MinAction)
        
        # 5分钟  
        downsample5MinAction = QAction('5分钟', self)
        downsample5MinAction.triggered.connect(lambda: self._downsampleMin1Data(5))
        downsampleMenu.addAction(downsample5MinAction)
        
        # 10分钟
        downsample10MinAction = QAction('10分钟', self)
        downsample10MinAction.triggered.connect(lambda: self._downsampleMin1Data(10))
        downsampleMenu.addAction(downsample10MinAction)
        
        # 15分钟
        downsample15MinAction = QAction('15分钟', self)
        downsample15MinAction.triggered.connect(lambda: self._downsampleMin1Data(15))
        downsampleMenu.addAction(downsample15MinAction)
        
        # 30分钟
        downsample30MinAction = QAction('30分钟', self)
        downsample30MinAction.triggered.connect(lambda: self._downsampleMin1Data(30))
        downsampleMenu.addAction(downsample30MinAction)
        
        # 60分钟
        downsample60MinAction = QAction('60分钟', self)
        downsample60MinAction.triggered.connect(lambda: self._downsampleMin1Data(60))
        downsampleMenu.addAction(downsample60MinAction)
        
        # 分隔线
        downsampleMenu.addSeparator()
        
        # 自定义
        downsampleCustomAction = QAction('自定义...', self)
        downsampleCustomAction.triggered.connect(self._downsampleMin1DataCustom)
        downsampleMenu.addAction(downsampleCustomAction)

        # 分隔线
        downsampleMenu.addSeparator()

        # 批量降采样
        batchDownsampleAction = QAction('批量降采样...', self)
        batchDownsampleAction.triggered.connect(self._batchDownsample)
        downsampleMenu.addAction(batchDownsampleAction)

        # 降采样状态
        downsampleStatusAction = QAction('降采样状态', self)
        downsampleStatusAction.triggered.connect(self._showDownsampleStatus)
        downsampleMenu.addAction(downsampleStatusAction)

        # 清理降采样数据
        cleanDownsampleAction = QAction('清理降采样数据...', self)
        cleanDownsampleAction.triggered.connect(self._cleanDownsampleData)
        downsampleMenu.addAction(cleanDownsampleAction)

        # 添加实盘数据菜单
        realReadyDataMenu = menuBar.addMenu('实盘准备数据')
        realReadyDataMenu.addAction(self._testedStocksAction)
        realReadyDataMenu.addAction(self._strategyDataPrepareAction)


        # 创建一个右对齐的菜单容器
        rightContainer = QWidget()
        rightLayout = QHBoxLayout(rightContainer)
        rightLayout.setContentsMargins(0, 0, 0, 0)
        rightLayout.setSpacing(0)
    
        # 添加弹簧推动右侧菜单
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        rightLayout.addWidget(spacer)
    
        # 创建右侧三个菜单
        updateToolsMenu = QMenuBar()
        updateToolsMenu.setStyleSheet("""
            QMenuBar {
                spacing: 0px;
                padding: 0px;
                margin: 0px;
            }
            QMenuBar::item {
                padding: 2px 6px;
                margin: 0px;
            }
        """)
        updateMenu = updateToolsMenu.addMenu('更新工具')
        updateMenu.addAction(self._oneKeyUpdateAction)
        updateMenu.addAction(self._timeOneKeyUpdateAction)
        rightLayout.addWidget(updateToolsMenu)
    
        dataSourceMenu = QMenuBar()
        dataSourceMenu.setStyleSheet("""
            QMenuBar {
                spacing: 0px;
                padding: 0px;
                margin: 0px;
            }
            QMenuBar::item {
                padding: 2px 6px;
                margin: 0px;
            }
        """)
        histTicksDataSourceMenu = dataSourceMenu.addMenu('分笔数据源')
        dataSourceGroup = QActionGroup(self)
        dataSourceGroup.setExclusive(True)
        for source in ['XTquant', '腾讯', '通达信', '智能']:
            action = QAction(source, self)
            action.setCheckable(True)
            action.triggered.connect(self._histTicksDataSourceAct)
            dataSourceGroup.addAction(action)
            histTicksDataSourceMenu.addAction(action)
            if source == DyStockDataCommon.defaultHistTicksDataSource:
                action.setChecked(True)
        rightLayout.addWidget(dataSourceMenu)
    
        viewMenuBar = QMenuBar()
        viewMenuBar.setStyleSheet("""
            QMenuBar {
                spacing: 0px;
                padding: 0px;
                margin: 0px;
            }
            QMenuBar::item {
                padding: 2px 6px;
                margin: 0px;
            }
        """)
        viewMenu = viewMenuBar.addMenu('视图和日志@@')
        viewMenu.addAction(self._enableLogDetailsAction)
        viewMenu.addAction(self._showLogAction)
        viewMenu.addAction(self._showProgressAction)
        rightLayout.addWidget(viewMenuBar)
    
        # 将右侧容器添加到菜单栏
        menuBar.setCornerWidget(rightContainer)
        
                                    
    def _toggleDockWidget(self, dock):
        """切换dock widget的显示状态"""
        if dock.isVisible():
            dock.hide()
        else:
            dock.show()
            # 如果显示时不是浮动状态，强制设置布局
            if not dock.isFloating():
                self.addDockWidget(Qt.TopDockWidgetArea, self._dockLog)
                self.addDockWidget(Qt.TopDockWidgetArea, self._dockProgress)
                self.splitDockWidget(self._dockLog, self._dockProgress, Qt.Vertical)
                self._adjustDockSizes()

    def _strategyDataPrepare(self):
        if self._strategyDataPrepareAction.text() == '停止':
            self._mainEngine._info.print('停止实盘策略准备数据和持仓准备数据...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopStockStrategyDataPrepareReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始策略准备数据
            data = {}
            if DyStockDataStrategyDataPrepareDlg(data, self).exec_():
                self._mainEngine._info.print('开始实盘策略准备数据和持仓准备数据...', DyLogData.ind)

                # change UI
                self._startRunningMutexAction(self._strategyDataPrepareAction)

                event = DyEvent(DyEventType.stockStrategyDataPrepare)
                event.data = data

                self._mainEngine.eventEngine.put(event)
        
    def closeEvent(self, event):
        """ 关闭事件 """
        self._mainEngine.exit()

        return super().closeEvent(event)

    def _testedStocks(self):
        isTested =  self._testedStocksAction.isChecked()

        codes = None
        if isTested:
            data = {}
            if DyStockSelectTestedStocksDlg(data).exec_():
                codes = data['codes']
            else:
                self._testedStocksAction.setChecked(False)

        # put event
        event = DyEvent(DyEventType.stockSelectTestedCodes)
        event.data = codes

        self._mainEngine.eventEngine.put(event)

    def _enableLogDetailsAct(self):
        DyStockDataCommon.logDetailsEnabled = self._enableLogDetailsAction.isChecked()
        
    def resizeEvent(self, event):
        super().resizeEvent(event)
        self._adjustDockSizes()

    def moveEvent(self, event):
        super().moveEvent(event)
        self._adjustDockSizes()

    def _dockFloatingChanged(self, dock):
        """处理dock widget的悬浮状态变化"""
        if not dock.isFloating():  # 如果dock被拖回主窗口
            # 重新设置布局
            self.addDockWidget(Qt.TopDockWidgetArea, self._dockLog)
            self.addDockWidget(Qt.TopDockWidgetArea, self._dockProgress)
            self.splitDockWidget(self._dockLog, self._dockProgress, Qt.Vertical)
            self._adjustDockSizes()

    def _sectorDataMannualUpdate(self):
        """手动更新板块数据"""
        if self._sectorDataMannualUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止板块数据更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockSectorDataReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始手动更新
            self._mainEngine._info.print('开始手动更新板块数据...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._sectorDataMannualUpdateAction)

            event = DyEvent(DyEventType.updateStockSectorData)
            event.data = {'mode': 'manual'}

            self._mainEngine.eventEngine.put(event)

    def _sectorDataAutoUpdate(self):
        """自动更新板块数据"""
        if self._sectorDataAutoUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止板块数据自动更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockSectorDataReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始自动更新
            self._mainEngine._info.print('开始自动更新板块数据...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._sectorDataAutoUpdateAction)

            event = DyEvent(DyEventType.updateStockSectorData)
            event.data = {'mode': 'auto'}

            self._mainEngine.eventEngine.put(event)

    def _sectorDataForcedUpdate(self):
        """强制更新板块数据"""
        if self._sectorDataForcedUpdateAction.text() == '停止':
            self._mainEngine._info.print('停止板块数据强制更新...', DyLogData.ind)

            # change UI
            self._stopRunningMutexAction()

            event = DyEvent(DyEventType.stopUpdateStockSectorDataReq)
            self._mainEngine.eventEngine.put(event)

        else: # 开始强制更新
            self._mainEngine._info.print('开始强制更新板块数据...', DyLogData.ind)

            # change UI
            self._startRunningMutexAction(self._sectorDataForcedUpdateAction)

            event = DyEvent(DyEventType.updateStockSectorData)
            event.data = {'mode': 'forced'}

            self._mainEngine.eventEngine.put(event)

    def _databaseHealthCheck(self):
        """数据库健康检查"""
        try:
            self._mainEngine._info.print('开始数据库健康检查...', DyLogData.ind)
            
            # 获取健康状态
            health = self._mainEngine._dataEngine.mongoDbEngine.checkDatabaseHealth()
            
            # 显示检查结果
            self._mainEngine._info.print('=' * 50, DyLogData.ind)
            self._mainEngine._info.print('数据库健康检查结果:', DyLogData.ind)
            self._mainEngine._info.print('=' * 50, DyLogData.ind)
            
            if health['connection']:
                self._mainEngine._info.print('✓ 数据库连接: 正常', DyLogData.ind)
            else:
                self._mainEngine._info.print('✗ 数据库连接: 异常', DyLogData.error)
            
            if health['indexes']:
                self._mainEngine._info.print('✓ 索引状态: 正常', DyLogData.ind)
            else:
                self._mainEngine._info.print('✗ 索引状态: 异常', DyLogData.warning)
            
            self._mainEngine._info.print(f'📊 集合数量: {health["collections"]}', DyLogData.ind)
            
            if health['issues']:
                self._mainEngine._info.print('⚠️  发现的问题:', DyLogData.warning)
                for issue in health['issues']:
                    self._mainEngine._info.print(f'  - {issue}', DyLogData.warning)
            else:
                self._mainEngine._info.print('✓ 未发现问题', DyLogData.ind)
            
            self._mainEngine._info.print('=' * 50, DyLogData.ind)
            
        except Exception as ex:
            self._mainEngine._info.print(f'健康检查异常: {ex}', DyLogData.error)

    def _databaseOptimize(self):
        """数据库优化"""
        try:
            # 询问用户确认
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(self, '数据库优化', 
                                       '数据库优化可能需要较长时间，确定要继续吗？\n\n操作内容：\n- 创建缺失的索引\n- 优化集合性能',
                                       QMessageBox.Yes | QMessageBox.No, 
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self._mainEngine._info.print('开始数据库优化...', DyLogData.ind)
                self._mainEngine._dataEngine.mongoDbEngine.optimizeDatabase()
                self._mainEngine._info.print('数据库优化完成', DyLogData.ind)
            else:
                self._mainEngine._info.print('取消数据库优化', DyLogData.ind)
                
        except Exception as ex:
            self._mainEngine._info.print(f'数据库优化异常: {ex}', DyLogData.error)

    def _databaseRepair(self):
        """数据库修复"""
        try:
            # 询问用户确认
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(self, '数据库修复', 
                                       '数据库修复可能需要较长时间，确定要继续吗？\n\n操作内容：\n- 健康检查\n- 修复索引问题\n- 清理无效数据',
                                       QMessageBox.Yes | QMessageBox.No, 
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self._mainEngine._info.print('开始数据库修复...', DyLogData.ind)
                success = self._mainEngine._dataEngine.mongoDbEngine.repairDatabase()
                
                if success:
                    self._mainEngine._info.print('数据库修复完成', DyLogData.ind)
                else:
                    self._mainEngine._info.print('数据库修复失败', DyLogData.error)
            else:
                self._mainEngine._info.print('取消数据库修复', DyLogData.ind)
                
        except Exception as ex:
            self._mainEngine._info.print(f'数据库修复异常: {ex}', DyLogData.error)

    def _databaseStats(self):
        """集合统计信息"""
        try:
            self._mainEngine._info.print('获取集合统计信息...', DyLogData.ind)
            
            # 获取股票代码列表
            codes = self._mainEngine._dataEngine.mongoDbEngine.getStockCodes()
            if not codes:
                self._mainEngine._info.print('无法获取股票代码列表', DyLogData.error)
                return
            
            # 显示统计信息
            self._mainEngine._info.print('=' * 60, DyLogData.ind)
            self._mainEngine._info.print('集合统计信息（显示前10个）:', DyLogData.ind)
            self._mainEngine._info.print('=' * 60, DyLogData.ind)
            self._mainEngine._info.print(f'{"股票代码":<12} {"文档数量":<10} {"大小(MB)":<10} {"最早日期":<12} {"最新日期":<12}', DyLogData.ind)
            self._mainEngine._info.print('-' * 60, DyLogData.ind)
            
            total_docs = 0
            total_size = 0
            
            # 只显示前10个股票的统计信息
            for i, code in enumerate(codes[:10]):
                stats = self._mainEngine._dataEngine.mongoDbEngine.getCollectionStats(code['code'])
                if stats:
                    size_mb = stats['size_bytes'] / (1024 * 1024) if stats['size_bytes'] else 0
                    earliest = stats['earliest_date'].strftime('%Y-%m-%d') if stats['earliest_date'] else 'N/A'
                    latest = stats['latest_date'].strftime('%Y-%m-%d') if stats['latest_date'] else 'N/A'
                    
                    self._mainEngine._info.print(
                        f'{code["code"]:<12} {stats["document_count"]:<10} {size_mb:<10.2f} {earliest:<12} {latest:<12}', 
                        DyLogData.ind
                    )
                    
                    total_docs += stats['document_count']
                    total_size += stats['size_bytes']
            
            if len(codes) > 10:
                self._mainEngine._info.print(f'... 还有{len(codes)-10}个集合未显示', DyLogData.ind)
            
            self._mainEngine._info.print('-' * 60, DyLogData.ind)
            self._mainEngine._info.print(f'总计: {total_docs} 条文档, {total_size/(1024*1024):.2f} MB (前10个集合)', DyLogData.ind)
            self._mainEngine._info.print('=' * 60, DyLogData.ind)
            
        except Exception as ex:
            self._mainEngine._info.print(f'获取集合统计信息异常: {ex}', DyLogData.error)

