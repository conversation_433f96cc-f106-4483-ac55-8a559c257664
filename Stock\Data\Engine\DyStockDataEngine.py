from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from .DyStockMongoDbEngine import *
from ..Gateway.DyStockDataGateway import *

from .DyStockDataTicksEngine import *
from .DyStockDataMin1Engine import *
from .DyStockDataMin5Engine import *
from .DyStockDataDaysEngine import *
from .DyStockDataFinancialTableEngine import *
from .DyStockDataStrategyDataPrepareEngine import *
from .DyStockDataDownsampleEngine import *
from .DyStockDataSectorEngine import *
from .DyStockDataUnifiedEngine import *
from .DyStockDataSmartManager import *
from .DyStockDataBaseTableManager import *


class DyStockDataEngine(object):

    class State:
        sWaitingTicks = 'sWaitingTicks'
        sWaitingMin1 = 'sWaitingMin1'
        sWaitingMin5 = 'sWaitingMin5'
        sWaitingDays = 'sWaitingDays'
        sWaitingFinancialTable = 'sWaitingFinancialTable'
        
    def __init__(self, eventEngine, info, registerEvent=True, dbCache=False):
        self._eventEngine = eventEngine
        self._info = info

        self._mongoDbEngine = DyStockMongoDbEngine(self._info, dbCache)
        self._gateway = DyStockDataGateway(self._eventEngine, self._info, registerEvent)

        self._daysEngine = DyStockDataDaysEngine(self._eventEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent)
        self._min1Engine = DyStockDataMin1Engine(self._eventEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent)
        self._min5Engine = DyStockDataMin5Engine(self._eventEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent)
        self._ticksEngine = DyStockDataTicksEngine(self._eventEngine, self._daysEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent)
        self._financialTableEngine = DyStockDataFinancialTableEngine(self._eventEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent)
        self._downsampleEngine = DyStockDataDownsampleEngine(self._eventEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent, dataEngine=self)
        self._sectorEngine = DyStockDataSectorEngine(self._eventEngine, self._mongoDbEngine, self._gateway, self._info, registerEvent)

        self._strategyDataPrepareEngine = DyStockDataStrategyDataPrepareEngine(self._eventEngine, self, self._info, registerEvent)

        # 基础表管理器
        self._baseTableManager = DyStockDataBaseTableManager(self._mongoDbEngine, self._gateway, self._info)

        # 统一数据访问引擎
        self._unifiedEngine = DyStockDataUnifiedEngine(self)

        # 智能数据管理器
        self._smartManager = DyStockDataSmartManager(self)

        # 初始化时检查基础表
        self._checkBaseTables()

        self._isStopped = False
        self._updateDates = None
        self._oneKeyUpdateState = None

        if registerEvent:
            self._registerEvent()

    @property
    def daysEngine(self):
        return self._daysEngine

    @property
    def ticksEngine(self):
        return self._ticksEngine

    @property
    def min1Engine(self):
        return self._min1Engine

    @property
    def min5Engine(self):
        return self._min5Engine

    @property
    def financialTableEngine(self):
        return self._financialTableEngine

    @property
    def sectorEngine(self):
        return self._sectorEngine

    @property
    def eventEngine(self):
        return self._eventEngine

    @property
    def info(self):
        return self._info

    @property
    def mongoDbEngine(self):
        return self._mongoDbEngine
        
    @property
    def gateway(self):
        return self._gateway

    @property
    def unifiedEngine(self):
        return self._unifiedEngine

    @property
    def smartManager(self):
        return self._smartManager

    @property
    def downsampleEngine(self):
        return self._downsampleEngine

    @property
    def baseTableManager(self):
        return self._baseTableManager

    def getDownsampleData(self, code, period, startDate, endDate, indicators, name=None):
        """
        获取降采样数据的包装方法
        如果数据不存在或不完整，会触发降采样生成
        @param code: 股票代码
        @param period: 降采样周期（分钟）
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param indicators: 指标列表
        @param name: 股票名称
        @return: DataFrame或None
        """
        # 检查基础表
        self._checkBaseTables()

        # 先尝试获取数据
        df = self._mongoDbEngine.getOneCodeDownsample(code, period, startDate, endDate, indicators, name)

        # 检查数据是否存在或完整
        if df is None or df.empty:
            # 获取降采样数据的最新日期
            latestDate = self._mongoDbEngine.getDownsampleLatestDate(period)

            # 检查是否需要生成降采样数据
            needUpdate = False
            if latestDate is None:
                # 完全没有数据，需要全量生成
                needUpdate = True
                self._info.print(f'没有找到{period}分钟降采样数据，需要生成', DyLogData.ind)
            elif latestDate < endDate:
                # 数据不是最新的，需要增量更新
                needUpdate = True
                self._info.print(f'{period}分钟降采样数据不是最新的，最新日期为{latestDate}，需要更新到{endDate}', DyLogData.ind)

            if needUpdate:
                # 触发降采样生成
                event = DyEvent(DyEventType.downsampleMin1Data)
                event.data = {'period': period}
                self._eventEngine.put(event)

                # 等待降采样完成
                self._info.print(f'正在生成{period}分钟降采样数据，请稍候...', DyLogData.warning)

                # 这里简单返回None，实际使用时应该等待降采样完成
                # 可以通过事件机制或其他方式实现同步等待
                return None

        return df

    def _checkBaseTables(self, forceCheck=False):
        """
        检查基础表完整性
        @param forceCheck: 是否强制检查
        @return: bool
        """
        try:
            # 使用基础表管理器检查
            return self._baseTableManager.ensureBaseTablesReady(forceCheck)
        except Exception as ex:
            self._info.print(f'基础表检查异常: {ex}', DyLogData.error)
            return False

    def forceUpdateBaseTables(self):
        """
        强制更新基础表
        @return: bool
        """
        try:
            return self._baseTableManager.forceUpdateBaseTables()
        except Exception as ex:
            self._info.print(f'强制更新基础表异常: {ex}', DyLogData.error)
            return False

    def getBaseTableStatus(self):
        """
        获取基础表状态
        @return: dict
        """
        try:
            return self._baseTableManager.getBaseTableStatus()
        except Exception as ex:
            self._info.print(f'获取基础表状态异常: {ex}', DyLogData.error)
            return None

    def _registerEvent(self):
        self._eventEngine.register(DyEventType.stockOneKeyUpdate, self._stockOneKeyUpdateHandler)
        self._eventEngine.register(DyEventType.stopStockOneKeyUpdateReq, self._stopStockOneKeyUpdateReqHandler)
        self._eventEngine.register(DyEventType.stockDaysCommonUpdateFinish, self._stockDaysCommonUpdateFinishHandler)

        self._eventEngine.register(DyEventType.stopAck, self._stopAckHandler)
        self._eventEngine.register(DyEventType.finish, self._finishHandler)
        self._eventEngine.register(DyEventType.fail, self._failHandler)

    def _stockOneKeyUpdateHandler(self, event):
        if self._oneKeyUpdateState is None:

            # 自动更新日线数据
            event = DyEvent(DyEventType.updateStockHistDays)
            event.data = None

            self._eventEngine.put(event)

            self._isStopped = False
            self._updateDates = None
            self._oneKeyUpdateState = DyStockDataEngine.State.sWaitingDays

    def _stopStockOneKeyUpdateReqHandler(self, event):
        self._isStopped = True

        if self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingDays:
            self._eventEngine.put(DyEvent(DyEventType.stopUpdateStockHistDaysReq))
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingTicks:
            self._eventEngine.put(DyEvent(DyEventType.stopUpdateStockHistTicksReq))
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin1:
            self._eventEngine.put(DyEvent(DyEventType.stopUpdateStockHistMin1Req))
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin5:
            self._eventEngine.put(DyEvent(DyEventType.stopUpdateStockHistMin5Req))
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingFinancialTable:
            self._eventEngine.put(DyEvent(DyEventType.stopUpdateStockHistFinancialTableReq))

    def _stockDaysCommonUpdateFinishHandler(self, event):
        if self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingDays:
            self._updateDates = event.data

    def _finishHandler(self, event):
        if self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingDays:
            if self._isStopped:
                self._eventEngine.put(DyEvent(DyEventType.stopAck))
                self._oneKeyUpdateState = None
            else:
                if self._updateDates is not None:
                    event = DyEvent(DyEventType.updateStockHistTicks)
                    event.data = self._updateDates
                    self._eventEngine.put(event)
                    self._oneKeyUpdateState = DyStockDataEngine.State.sWaitingTicks
                else:
                    # 开始更新1分钟数据
                    event = DyEvent(DyEventType.updateStockHistMin1)
                    event.data = None
                    self._eventEngine.put(event)
                    self._oneKeyUpdateState = DyStockDataEngine.State.sWaitingMin1

        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingTicks:
            # Ticks更新完成后，更新1分钟数据
            event = DyEvent(DyEventType.updateStockHistMin1)
            event.data = None
            self._eventEngine.put(event)
            self._oneKeyUpdateState = DyStockDataEngine.State.sWaitingMin1

        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin1:
            # 1分钟数据更新完成后，更新5分钟数据
            event = DyEvent(DyEventType.updateStockHistMin5)
            event.data = None
            self._eventEngine.put(event)
            self._oneKeyUpdateState = DyStockDataEngine.State.sWaitingMin5

        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin5:
            # 5分钟数据更新完成后，更新财务数据
            event = DyEvent(DyEventType.updateStockHistFinancialTable)
            event.data = None
            self._eventEngine.put(event)
            self._oneKeyUpdateState = DyStockDataEngine.State.sWaitingFinancialTable

        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingFinancialTable:
            # 所有数据更新完成
            self._oneKeyUpdateState = None

    def _failHandler(self, event):
        if self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingDays:
            self._eventEngine.put(DyEvent(DyEventType.fail)) # for UI
            self._oneKeyUpdateState = None

        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingTicks:
            self._oneKeyUpdateState = None
        
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin1:
            self._eventEngine.put(DyEvent(DyEventType.fail)) # for UI
            self._oneKeyUpdateState = None
        
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin5:
            self._eventEngine.put(DyEvent(DyEventType.fail)) # for UI
            self._oneKeyUpdateState = None
        
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingFinancialTable:
            self._eventEngine.put(DyEvent(DyEventType.fail)) # for UI
            self._oneKeyUpdateState = None

    def _stopAckHandler(self, event):
        if self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingDays:
            self._eventEngine.put(DyEvent(DyEventType.stopAck)) # for UI
            self._oneKeyUpdateState = None

        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingTicks:
            self._oneKeyUpdateState = None
        
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin1:
            self._eventEngine.put(DyEvent(DyEventType.stopAck)) # for UI
            self._oneKeyUpdateState = None
        
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingMin5:
            self._eventEngine.put(DyEvent(DyEventType.stopAck)) # for UI
            self._oneKeyUpdateState = None
        
        elif self._oneKeyUpdateState == DyStockDataEngine.State.sWaitingFinancialTable:
            self._eventEngine.put(DyEvent(DyEventType.stopAck)) # for UI
            self._oneKeyUpdateState = None

    def getSectorData(self, date, sector_type=None):
        """
        获取板块数据
        @param date: 日期，格式YYYY-MM-DD
        @param sector_type: 板块类型，None表示获取所有类型
        @return: {sector_type: {sector_code: sector_name}} or None
        """
        return self._mongoDbEngine.getSectorData(date, sector_type)

    def getSectorStocksData(self, date, sector_code=None):
        """
        获取板块成分股数据
        @param date: 日期，格式YYYY-MM-DD
        @param sector_code: 板块代码，None表示获取所有板块
        @return: {sector_code: [{'stock_code': xxx, 'stock_name': xxx}]} or None
        """
        return self._mongoDbEngine.getSectorStocksData(date, sector_code)

    def getLatestSectorDate(self):
        """
        获取板块数据的最新日期
        @return: 最新日期字符串或None
        """
        return self._mongoDbEngine.getLatestSectorDate()

    def getSectorsByStock(self, stock_code, date):
        """
        获取指定股票所属的板块
        @param stock_code: 股票代码
        @param date: 日期，格式YYYY-MM-DD
        @return: [sector_code] or []
        """
        return self._mongoDbEngine.getSectorsByStock(stock_code, date)
