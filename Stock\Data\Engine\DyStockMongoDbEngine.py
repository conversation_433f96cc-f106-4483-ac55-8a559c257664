import pymongo
import pandas as pd
from time import sleep
from datetime import datetime, timedelta
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from pymongo import UpdateOne
import time

from DyCommon.DyCommon import *
from ...Common.DyStockCommon import *
from Stock.Data.Engine.DyStockDbCache import DyGetStockDbCache


class DyStockMongoDbEngine(object):
    host = 'localhost'
    port = 27017

    stockTicksDb = 'stockTicksDb' # 股票分笔数据

    # default DB for Wind Data Source
    stockCommonDb = 'stockCommonDb'
    tradeDayTableName = "tradeDayTable"
    codeTableName = "codeTable"

    stockDaysDb = 'stockDaysDb' # 股票日线行情数据

    # DB for TuShare Data Source
    stockCommonDbTuShare = 'stockCommonDbTuShare'
    tradeDayTableNameTuShare = "tradeDayTableTuShare"
    codeTableNameTuShare = "codeTableTuShare"

    stockDaysDbTuShare = 'stockDaysDbTuShare' # 股票日线行情数据
    
    # DB for XTquant Data Source
    stockCommonDbXTquant = 'stockCommonDbXTquant'
    tradeDayTableNameXTquant = "tradeDayTableXTquant"
    codeTableNameXTquant = "codeTableXTquant"

    BalanceDbXTquant = "BalanceDbXTquant"
    IncomeDbXTquant = "IncomeDbXTquant"
    CashFlowDbXTquant = "CashFlowDbXTquant"
    CapitalDbXTquant = "CapitalDbXTquant"
    PershareIndexDbXTquant = "PershareIndexDbXTquant"
    Top10holderDbXTquant = "Top10holderDbXTquant"
    HoldernumDbXTquant = "HoldernumDbXTquant"

    stock1minDbXTquant = 'stock1minDbXTquant' # 股票1分钟行情数据
    stock5minDbXTquant = 'stock5minDbXTquant' # 股票5分钟行情数据
    stockDaysDbXTquant = 'stockDaysDbXTquant' # 股票日线行情数据

    # 降采样数据库
    stockDownsampleDbXTquant = 'stockDownsampleDbXTquant' # 股票降采样行情数据

    # 板块数据数据库
    stockSectorDbXTquant = 'stockSectorDbXTquant'  # 板块基本信息数据库
    stockSectorStocksDbXTquant = 'stockSectorStocksDbXTquant'  # 板块成分股数据库

    # 板块股票代码表,一个日期对应一张表
    sectorCodeDbMap = {DyStockCommon.sz50Index: 'sz50CodeTableDb',
                       DyStockCommon.hs300Index: 'hs300CodeTableDb',
                       DyStockCommon.zz500Index: 'zz500CodeTableDb'
                       }

    
    def __init__(self, info, cache=False):
        self._info = info
        
        # 添加连接选项
        client_options = {
            'serverSelectionTimeoutMS': 5000,
            'connectTimeoutMS': 10000,
            'retryWrites': True,
            'w': 1,                    # 写入确认
            'maxPoolSize': 100         # 连接池大小
        }
        
        self._client = pymongo.MongoClient(
            self.host, 
            self.port,
            **client_options
        )

        # DB cache
        self._dbCache = None
        if cache:
            self._dbCache = DyGetStockDbCache(self._info, self)

        # 添加缓存相关的属性初始化
        self._latestDateCache = {}  # {db_name: latest_date}
        self._cacheExpireTime = None  # 缓存过期时间

        # 创建索引
        self._createFinancialIndexes()
        
        # 初始化板块数据集合（修复：确保在初始化时调用）
        self._initializeSectorCollections()

    def _createAllIndexes(self):
        """创建所有必要的索引"""
        try:
            # 1. 创建财务数据索引
            self._createFinancialIndexes()
            
            # 2. 创建行情数据索引
            self._createIndexes()
            
            # 3. 创建交易日历索引
            collection = self._getTradeDayTableCollection()
            collection.create_index([('datetime', -1)])
            
        except Exception as ex:
            self._info.print(f"创建索引异常: {ex}", DyLogData.warning)

    def _createFinancialIndexes(self):
        """创建财务数据集合的索引"""
        financial_dbs = [
            self.BalanceDbXTquant,
            self.IncomeDbXTquant,
            self.CashFlowDbXTquant,
            self.CapitalDbXTquant,
            self.PershareIndexDbXTquant,
            self.Top10holderDbXTquant,
            self.HoldernumDbXTquant
        ]
        
        for db_name in financial_dbs:
            try:
                # 获取数据库
                db = self._client[db_name]
                
                # 获取所有已存在的集合
                collections = db.list_collection_names()
                
                # 为每个集合创建索引
                for collection_name in collections:
                    collection = db[collection_name]
                    # 检查索引是否已存在
                    if 'code_datetime_idx' not in collection.index_information():
                        collection.create_index([
                            ('code', pymongo.ASCENDING),
                            ('datetime', pymongo.ASCENDING)
                        ], name='code_datetime_idx')
                        
            except Exception as ex:
                self._info.print(f"创建财务数据索引异常[{db_name}]: {ex}", DyLogData.warning)
                continue

    def updateFinancialData(self, code, data, db_name):
        """
        更新财务数据到指定的数据库
        """
        try:
            # 获取数据库和集合
            db = self._client[db_name]
            
            # 确保code不为'default'
            if code == 'default':
                return False
            
            collection = db[code]
            
            # 批量更新
            if data:
                operations = []
                for record in data:
                    operations.append(
                        pymongo.UpdateOne(
                            {'datetime': record['datetime']},
                            {'$set': record},
                            upsert=True
                        )
                    )
                
                if operations:
                    collection.bulk_write(operations, ordered=False)
                
            return True
            
        except Exception as ex:
            self._info.print(f"更新财务数据到{db_name}异常: {ex}", DyLogData.error)
            return False

    def getFinancialData(self, code, table_name, startDate, endDate):
        """
        使用聚合管道从数据库获取财务数据
        @param code: 股票代码
        @param table_name: 报表类型(如'Balance')
        @param startDate: 开始日期 'YYYY-MM-DD'
        @param endDate: 结束日期 'YYYY-MM-DD'
        @return: DataFrame or None
        """
        try:
            # 获取对应的数据库集合
            collection = self._client[getattr(self, f"{table_name}DbXTquant")][code]
            
            # 转换日期
            try:
                dateStart = datetime.strptime(startDate, '%Y-%m-%d')
                dateEnd = datetime.strptime(endDate + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            except ValueError as e:
                self._info.print(f"日期格式错误[{code}-{table_name}]: {e}", DyLogData.error)
                return None
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 排除_id字段
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline, allowDiskUse=True)
            
            # 转换为DataFrame
            df = pd.DataFrame(list(cursor))
            if not df.empty:
                df.set_index('datetime', inplace=True)
            
            return df
            
        except Exception as ex:
            self._info.print(f"从MongoDB获取财务数据[{code}-{table_name}]异常: {ex}", DyLogData.error)
            return None

    def _getTradeDayTableCollection(self):
        if 'XTquant' in DyStockCommon.defaultHistDaysDataSource:
            collection = self._client[self.stockCommonDbXTquant][self.tradeDayTableNameXTquant]
        elif 'Wind' in DyStockCommon.defaultHistDaysDataSource:
            collection = self._client[self.stockCommonDb][self.tradeDayTableName]
        else:
            collection = self._client[self.stockCommonDbTuShare][self.tradeDayTableNameTuShare]

        return collection

    def _getCodeTableCollection(self):
        if 'XTquant' in DyStockCommon.defaultHistDaysDataSource:
            collection = self._client[self.stockCommonDbXTquant][self.codeTableNameXTquant]
        elif 'Wind' in DyStockCommon.defaultHistDaysDataSource:
            collection = self._client[self.stockCommonDb][self.codeTableName]
        else:
            collection = self._client[self.stockCommonDbTuShare][self.codeTableNameTuShare]

        return collection

    def _getStockDaysDb(self):
        if 'XTquant' in DyStockCommon.defaultHistDaysDataSource:
            db = self._client[self.stockDaysDbXTquant]
        elif 'Wind' in DyStockCommon.defaultHistDaysDataSource:
            db = self._client[self.stockDaysDb]
        else:
            db = self._client[self.stockDaysDbTuShare]

        return db

    def _deleteTicks(self, code, date):
        """
        删除指定股票在指定日期的分笔数据
        @param code: 股票代码
        @param date: 日期字符串，格式为'YYYY-MM-DD'
        @return: bool
        """
        collection = self._client[self.stockTicksDb][code]

        try:
            # 转换日期范围
            dateStart = datetime.strptime(date, '%Y-%m-%d')
            dateEnd = datetime.strptime(date + ' 23:00:00', '%Y-%m-%d %H:%M:%S')

            # 使用聚合管道查找要删除的文档
            pipeline = [
                {
                    '$match': {
                        'datetime': {
                            '$gt': dateStart,
                            '$lt': dateEnd
                        }
                    }
                }
            ]
            
            # 执行删除操作
            result = collection.delete_many(pipeline[0]['$match'])
            
            # 添加删除结果日志
            self._info.print(f"删除了{result.deleted_count}条Tick数据[{code},{date}]")
            
            return True
            
        except Exception as ex:
            error_msg = f"删除Tick数据[{code},{date}]引发MongoDB异常:"
            if hasattr(ex, 'details'):
                error_msg += f" {str(ex)}, {str(ex.details)}"
            else:
                error_msg += f" {str(ex)}"
            
            self._info.print(error_msg, DyLogData.error)
            return False

    def _findTicks(self, code, startDate, endDate):
        """查找分笔数据"""
        collection = self._client[self.stockTicksDb][code]
        
        start, end = self._getDateRange(startDate, endDate)
        if not start or not end:
            return None, 0
        
        try:
            # 添加调试信息
            self._info.print(f"查询{code}的分笔数据: {start} - {end}")
            
            # 使用聚合管道替代find
            pipeline = [
                {
                    '$match': {
                        'datetime': {
                            '$gte': start,
                            '$lte': end
                        }
                    }
                },
                {
                    '$sort': {
                        'datetime': 1
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            
            # 获取文档数量
            count_pipeline = [
                {
                    '$match': {
                        'datetime': {
                            '$gte': start,
                            '$lte': end
                        }
                    }
                },
                {
                    '$count': 'total'
                }
            ]
            count_result = list(collection.aggregate(count_pipeline))
            count = count_result[0]['total'] if count_result else 0
            
            self._info.print(f"查询到{count}条分笔记录")
            
            return cursor, count
            
        except Exception as ex:
            self._handleMongoError(ex)
            return None, 0

    def _findTradeDays(self, startDate=None, endDate=None):
        """
        查找交易日数据，使用聚合管道处理
        @startDate: YYYY-MM-DD
        @endDate: YYYY-MM-DD
        """
        collection = self._getTradeDayTableCollection()
        
        try:
            # 构建聚合管道
            pipeline = []
            
            # 1. 日期过滤
            if startDate is not None:
                try:
                    dateStart = datetime.strptime(startDate, '%Y-%m-%d')
                    dateEnd = datetime.strptime(endDate, '%Y-%m-%d')
                    dateEnd = dateEnd.replace(hour=23, minute=59, second=59)
                    
                    self._info.print(f"查询交易日期范围: {dateStart} - {dateEnd}")
                    
                    pipeline.append({
                        '$match': {
                            'datetime': {
                                '$gte': dateStart,
                                '$lte': dateEnd
                            }
                        }
                    })
                except ValueError as e:
                    self._info.print(f"日期格式错误: {e}", DyLogData.error)
                    return None, 0
            
            # 2. 排序
            pipeline.append({
                '$sort': {
                    'datetime': 1
                }
            })
            
            # 3. 计数管道
            count_pipeline = pipeline.copy()
            count_pipeline.append({
                '$count': 'total'
            })
            
            # 获取计数
            count_result = list(collection.aggregate(count_pipeline))
            count = count_result[0]['total'] if count_result else 0
            
            # 添加结果验证
            self._info.print(f"查询到{count}条交易日记录")
            
            if count > 0:
                # 执行两次查询而不是使用clone
                # 1. 先获取第一条记录用于验证
                first_pipeline = pipeline.copy()
                first_pipeline.append({'$limit': 1})
                first_result = list(collection.aggregate(first_pipeline))
                if first_result:
                    self._info.print(f"第一条记录: {first_result[0]}")
                
                # 2. 然后执行完整查询
                cursor = collection.aggregate(pipeline, allowDiskUse=True)
            else:
                cursor = collection.aggregate(pipeline, allowDiskUse=True)
            
            return cursor, count
            
        except Exception as ex:
            self._handleMongoError(ex)
            return None, 0

    def _getTradeDaysByRelativeNegative(self, baseDate, n):
        """
        获取相对于基准日期向前的n个交易日
        @baseDate: 基准日期
        @n: 向前偏移的交易日数量(负数)
        """
        baseDateSave = baseDate
        nSave = n

        # always get 0 offset trade day
        baseDate = self._getTradeDaysByRelativeZero(baseDate)
        if baseDate is None: return None

        # find forward n trade days
        collection = self._getTradeDayTableCollection()

        try:
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {'$lt': baseDate[0]['datetime']},
                        'tradeDay': True
                    }
                },
                # 2. 按日期降序排序
                {
                    '$sort': {
                        'datetime': -1
                    }
                },
                # 3. 限制返回记录数
                {
                    '$limit': abs(n)
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            
            # 处理结果
            dates = [baseDate[0]]
            results = list(cursor)
            
            if len(results) == abs(n):
                dates.extend(results)
                return dates
            
            self._info.print("数据库里没有{0}向前{1}个交易日的日期数据".format(baseDateSave, abs(nSave)),
                            DyLogData.error)
            return None

        except Exception as ex:
            self._info.print("MongoDB Exception({0}): @_getTradeDaysByRelativeNegative({1}, {2})".format(
                str(ex) + ', ' + str(ex.details) if hasattr(ex, 'details') else str(ex),
                baseDateSave, 
                nSave
            ), DyLogData.error)
            return None

    def _getTradeDaysByRelativeZero(self, baseDate):
        """ 
        基准日期向前找到第一个交易日 
        @baseDate: 基准日期
        @return: [交易日记录] or None
        """
        baseDateSave = baseDate

        collection = self._getTradeDayTableCollection()

        try:
            baseDate = datetime.strptime(baseDate, '%Y-%m-%d')
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {'$lte': baseDate},
                        'tradeDay': True
                    }
                },
                # 2. 按日期降序排序
                {
                    '$sort': {
                        'datetime': -1
                    }
                },
                # 3. 限制返回一条记录
                {
                    '$limit': 1
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            result = list(cursor)
            
            return [result[0]] if result else None

        except Exception as ex:
            self._info.print("MongoDB Exception({0}): @_getTradeDaysByRelativeZero({1})".format(
                str(ex) + ', ' + str(ex.details) if hasattr(ex, 'details') else str(ex),
                baseDateSave
            ), DyLogData.error)
            return None

    def _getTradeDaysByRelativePositive(self, baseDate, n):
        """
        获取相对于基准日期向后的n个交易日
        @baseDate: 基准日期
        @n: 向后偏移的交易日数量
        """
        baseDateSave = baseDate
        nSave = n

        # always get 0 offset trade day
        baseDate = self._getTradeDaysByRelativeZero(baseDate)
        if baseDate is None: 
            return None

        # find backward n trade days
        collection = self._getTradeDayTableCollection()

        try:
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$gt': baseDate[0]['datetime']
                        }
                    }
                },
                # 2. 按日期升序排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 过滤交易日
                {
                    '$match': {
                        'tradeDay': True
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            
            # 处理结果
            dates = [baseDate[0]]
            count = 0
            
            for d in cursor:
                dates.append(d)
                count += 1
                if count == n:
                    return dates

            # 如果数据库里的最新日期不是今日,提醒更新数据
            date = self.getDaysLatestDate()
            if date is not None:
                now = datetime.now()
                if now > datetime(now.year, now.month, now.day, 18, 0, 0) and DyTime.dateCmp(date['datetime'], now) != 0:
                    self._info.print("数据库的最新日期不是今日, 请更新历史日线数据", DyLogData.error)
                    return None

            return dates

        except Exception as ex:
            self._info.print("MongoDB Exception({0}): @_getTradeDaysByRelativePositive({1}, {2})".format(
                str(ex) + ', ' + str(ex.details) if hasattr(ex, 'details') else str(ex), 
                baseDateSave, 
                nSave
            ), DyLogData.error)
            return None

    def _findOneCodeDays(self, code, startDate, endDate, name=None):
        """查找个股日线数据"""
        collection = self._getStockDaysDb()[code]
    
        try:
            # 转换日期范围
            dateStart = datetime.strptime(startDate, '%Y-%m-%d')
            dateEnd = datetime.strptime(endDate, '%Y-%m-%d') + timedelta(days=1)  # 包含结束日期当天
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 排除_id字段
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            results = list(cursor)  # 立即转换游标到列表
            
            if not results:
                print("未找到数据，请检查日期范围和数据存在性")
                return None
                
            return results
            
        except Exception as ex:
            self._info.print(f"MongoDB Exception({str(ex)}): 查找{code}:{name}, [{startDate}, {endDate}]日线数据", DyLogData.error)
            return None
    
    def _getCodeDay(self, code, baseDate, name=None):
        """ 得到个股的当日交易日, 向前贪婪 """
        collection = self._getStockDaysDb()[code]
    
        try:
            date = datetime.strptime(baseDate + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$lt': date
                        }
                    }
                },
                # 2. 降序排序
                {
                    '$sort': {
                        'datetime': -1
                    }
                },
                # 3. 限制返回一条记录
                {
                    '$limit': 1
                },
                # 4. 投影所需字段
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1
                    }
                }
            ]
            
            # 执行聚合查询
            result = list(collection.aggregate(pipeline))
            
            if result:
                return result[0]['datetime'].strftime('%Y-%m-%d')
                
            return None
            
        except Exception as ex:
            self._info.print(f"MongoDB Exception({str(ex)}): @_getCodeDay {code}:{name}, [{baseDate}]", DyLogData.error)
            return None
    
    def _findOneCodeDaysByRelative(self, code, baseDate, n=0, name=None):
        """
        包含当日,也就是说offset 0总是被包含的
        """
        # 获取当日日期
        baseDay = self._getCodeDay(code, baseDate, name)
        if baseDay is None: 
            return None
    
        collection = self._getStockDaysDb()[code]
    
        try:
            if n <= 0:
                date = datetime.strptime(baseDay + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
                match_condition = {'datetime': {'$lt': date}}
                sort_order = -1  # 降序
            else:
                date = datetime.strptime(baseDay, '%Y-%m-%d')
                match_condition = {'datetime': {'$gte': date}}
                sort_order = 1  # 升序
    
            # 向前贪婪
            n = abs(n) + 1
    
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': match_condition
                },
                # 2. 排序
                {
                    '$sort': {
                        'datetime': sort_order
                    }
                },
                # 3. 限制返回记录数
                {
                    '$limit': n
                },
                # 4. 排除_id字段
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            return cursor
            
        except Exception as ex:
            self._info.print(f"MongoDB Exception({str(ex)}): @_findOneCodeDaysByRelative {code}:{name}, [{baseDate}, {n}]日线数据", 
                            DyLogData.error)
            return None

    def _getOneCodeDaysByCursor(self, cursor, indicators):
        try:
            columns = indicators + ['datetime']
            if 'adjfactor' not in columns:
                columns.append('adjfactor')
            df = pd.DataFrame(list(cursor), columns=columns)
            df = df.dropna(axis=1, how='all') # 去除全为NaN的列,比如指数数据,没有'mf_vol'
            df = df.set_index('datetime')

        except Exception as ex:
            return None

        return None if df.empty else df

    def _getOneCodeDaysUnified2(self, code, startDate, endDate, indicators, name=None):
        if isinstance(endDate, int):
            df = self._getOneCodeDaysByRelative(code, indicators, startDate, endDate, name)
        else:
            df = self.getOneCodeDays(code, startDate, endDate, indicators, name)

        return df

    def _getOneCodeDaysUnified3(self, code, startDate, endDate, n, indicators, name=None):
        # 分部分载入
        # front part
        startDateNew, endDateNew = startDate, endDate
        if isinstance(startDate, int):
            startDateNew, endDateNew = endDateNew, startDateNew

        frontDf = self._getOneCodeDaysUnified2(code, startDateNew, endDateNew, indicators, name)
        if frontDf is None: return None

        # back part
        backDf = self._getOneCodeDaysUnified2(code, endDate, n, indicators, name)
        if backDf is None: return None

        # concat front DF and back DF
        df = pd.concat([frontDf, backDf])

        # drop duplicated
        df = df[~df.index.duplicated()]

        return df

    def _getOneCodeDaysByRelative(self, code, indicators, baseDate, n=0, name=None):

        cursor = self._findOneCodeDaysByRelative(code, baseDate, n, name)
        if cursor is None: return None

        return self._getOneCodeDaysByCursor(cursor, indicators)


    # -------------------- 公共接口 --------------------
    def updateDays(self, code, data):
        """
        更新日线数据
        @param code: 股票代码
        @param data: 日线数据列表
        @return: bool
        """
        try:
            collection = self._getStockDaysDb()[code]
            
            # 检查现有索引
            existing_indexes = collection.index_information()
            datetime_index_exists = False
            old_index_name = None
            
            # 查找现有的datetime索引
            for index_name, index_info in existing_indexes.items():
                if len(index_info['key']) == 1 and index_info['key'][0][0] == 'datetime':
                    datetime_index_exists = True
                    old_index_name = index_name
                    break
            
            # 如果需要，更新索引
            if not datetime_index_exists:
                # 创建新索引
                collection.create_index(
                    [('datetime', pymongo.ASCENDING)],
                    unique=True,
                    background=True,
                    name='datetime_idx'
                )
            elif old_index_name != 'datetime_idx':
                # 如果存在旧索引但名称不同，先删除旧索引再创建新索引
                try:
                    collection.drop_index(old_index_name)
                    collection.create_index(
                        [('datetime', pymongo.ASCENDING)],
                        unique=True,
                        background=True,
                        name='datetime_idx'
                    )
                except Exception as ex:
                    # 如果无法更新索引，记录警告但继续执行
                    self._info.print(f"更新索引失败，继续使用现有索引: {ex}", DyLogData.warning)

            # 批量更新操作
            if data:
                operations = []
                for doc in data:
                    operations.append(
                        pymongo.UpdateOne(
                            {'datetime': doc['datetime']},
                            {'$set': doc},
                            upsert=True
                        )
                    )
                
                if operations:
                    # 添加写入选项
                    write_options = {
                        'ordered': False,  # 无序执行提高性能
                        'bypass_document_validation': True  # 跳过文档验证
                    }
                    
                    # 执行批量写入
                    result = collection.with_options(
                        write_concern=pymongo.WriteConcern(w=1)
                    ).bulk_write(operations, **write_options)
                    
                    # 添加操作结果日志
                    self._info.print(f"股票{code}日线数据更新结果: "
                                   f"新增{result.upserted_count}条, "
                                   f"修改{result.modified_count}条")
                    
            return True
            
        except pymongo.errors.BulkWriteError as ex:
            self._info.print(f"批量更新股票{code}日线数据时发生错误: {ex.details}", DyLogData.error)
            return False
            
        except Exception as ex:
            error_msg = f"更新股票{code}日线数据异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

    def updateTradeDays(self, dates):
        """
        批量更新交易日数据
        @param dates: 日期列表，每个元素包含datetime和tradeDay字段
        @return: bool
        """
        collection = self._getTradeDayTableCollection()

        try:
            # 创建索引(如果不存在)
            if 'datetime_idx' not in collection.index_information():
                collection.create_index([('datetime', pymongo.ASCENDING)], 
                                     unique=True,
                                     background=True,
                                     name='datetime_idx')

            # 准备批量更新操作
            operations = []
            for date in dates:
                operations.append(
                    pymongo.UpdateOne(
                        {'datetime': date['datetime']},
                        {'$set': {'tradeDay': date['tradeDay']}},
                        upsert=True
                    )
                )

            # 执行批量更新
            if operations:
                result = collection.bulk_write(operations, 
                                             ordered=False,
                                             bypass_document_validation=True)
                
                # 添加操作结果日志
                self._info.print(f"更新了{result.upserted_count}个新日期, "
                               f"修改了{result.modified_count}个现有日期")

            return True

        except pymongo.errors.BulkWriteError as ex:
            self._info.print(f"批量更新交易日数据时发生错误: {ex.details}", DyLogData.error)
            return False
        
        except Exception as ex:
            error_msg = f"更新交易日数据异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

    def updateStockCodes(self, codes):
        """
        批量更新股票代码表
        @param codes: 股票代码列表，每个元素包含code和name
        @return: bool
        """
        collection = self._getCodeTableCollection()

        try:
            # 创建索引(如果不存在)
            if 'code_idx' not in collection.index_information():
                collection.create_index([('code', pymongo.ASCENDING)], 
                                     unique=True,
                                     background=True,
                                     name='code_idx')

            # 准备批量更新操作
            operations = []
            for code in codes:
                operations.append(
                    pymongo.UpdateOne(
                        {'code': code['code']},
                        {'$set': {'name': code['name']}},
                        upsert=True
                    )
                )

            # 执行批量更新
            if operations:
                result = collection.bulk_write(operations, ordered=False)
                
                # 添加操作结果日志
                self._info.print(f"更新了{result.upserted_count}个新代码, "
                               f"修改了{result.modified_count}个现有代码")

            return True

        except pymongo.errors.BulkWriteError as ex:
            self._info.print(f"批量更新股票代码表时发生错误: {ex.details}", DyLogData.error)
            return False
        
        except Exception as ex:
            error_msg = f"更新股票代码表异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

    def getOneCodeDaysWrapper(func):
        """
            @getOneCodeDays的装饰器
        """
        def wrapper(self, *args, **kwargs):
            if self._dbCache is None or kwargs.get('raw'):
                return func(self, *args, **kwargs)

            return self._dbCache.getOneCodeDays(*args, **kwargs)

        return wrapper

    @getOneCodeDaysWrapper
    def getOneCodeDays(self, code, startDate, endDate, indicators, name=None, raw=False):
        """
            通过绝对日期获取个股日线数据
            @raw: True - not via cache, for called by DB cache
        """
        cursor = self._findOneCodeDays(code, startDate, endDate, name)
        if cursor is None: return None

        return self._getOneCodeDaysByCursor(cursor, indicators)

    def getDays(self, codes, startDate, endDate, indicators):
        """ @codes: [code] or {code:name}
            @return: {code:DF}
        """
        isDict = True if isinstance(codes, dict) else False

        codesDf = {}
        for code in codes:

            name = codes[code] if isDict else None
            df = self.getOneCodeDays(code, startDate, endDate, indicators, name)

            if df:
                codesDf[code] = df

        return codesDf if codesDf else None

    def getAdjFactorWrapper(func):
        """
            @getAdjFactor的装饰器
        """
        def wrapper(self, *args, **kwargs):
            if self._dbCache is None:
                return func(self, *args, **kwargs)

            return self._dbCache.getAdjFactor(*args, **kwargs)

        return wrapper

    @getAdjFactorWrapper
    def getAdjFactor(self, code, date, name=None):
        """
        获取指定股票在指定日期的复权因子
        @param code: 股票代码
        @param date: 日期
        @param name: 股票名称(用于日志)
        @return: float or None
        """
        try:
            collection = self._getStockDaysDb()[code]
            
            # 转换日期
            dateStart = datetime.strptime(date, '%Y-%m-%d')
            dateEnd = dateStart + timedelta(days=1)
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 只获取复权因子字段
                {
                    '$project': {
                        '_id': 0,
                        'adjfactor': 1
                    }
                },
                # 3. 限制返回一条记录
                {
                    '$limit': 1
                }
            ]
            
            # 执行聚合查询
            result = list(collection.aggregate(pipeline))
            
            if result and 'adjfactor' in result[0]:
                return float(result[0]['adjfactor'])
            
            return None
            
        except Exception as ex:
            self._info.print(f"获取{code}:{name}复权因子异常[{date}]: {ex}", DyLogData.error)
            return None

    def getDaysLatestDate(self):
        """ 获取数据库里交易日数据的最新日期,不是交易日 """
        while True:
            try:
                # 使用聚合管道查询最新日期
                collection = self._getTradeDayTableCollection()
                
                pipeline = [
                    {
                        '$sort': {
                            'datetime': -1
                        }
                    },
                    {
                        '$limit': 1
                    },
                    {
                        '$project': {
                            '_id': 0,
                        'datetime': 1
                        }
                    }
                ]
                
                # 执行聚合查询
                result = list(collection.aggregate(pipeline))
                if result:
                    return result[0]
                    
                return None

            except pymongo.errors.OperationFailure as ex:
                # MongoDB操作失败异常包含details属性
                error_msg = f"MongoDB操作失败: {ex.details if hasattr(ex, 'details') else str(ex)}"
                self._info.print(error_msg, DyLogData.error)
                return None
            
            except pymongo.errors.ServerSelectionTimeoutError as ex:
                # 连接超时异常
                self._info.print('MongoDB正在启动, 等待60s后重试...', DyLogData.warning)
                sleep(60)
                continue
            
            except Exception as ex:
                # 其他异常
                error_msg = f"MongoDB异常: {str(ex)}"
                self._info.print(error_msg, DyLogData.error)
                return None

    def getDaysLatestTradeDay(self):
        """
        获取数据库中最新的交易日
        @return: 日期字符串格式 'YYYY-MM-DD' 或 None
        """
        try:
            collection = self._getTradeDayTableCollection()
            
            # 使用聚合管道查询最新交易日
            pipeline = [
                # 1. 排序（降序）
                {
                    '$sort': {
                        'datetime': -1
                    }
                },
                # 2. 限制返回一条记录
                {
                    '$limit': 1
                },
                # 3. 投影所需字段
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            
            # 获取结果
            result = list(cursor)
            if result:
                # 转换为字符串格式返回，确保格式正确
                date_obj = result[0]['datetime']
                if isinstance(date_obj, datetime):
                    return date_obj.strftime('%Y-%m-%d')
                else:
                    # 如果已经是字符串，直接返回
                    return str(date_obj)
            else:
                return None
            
        except Exception as ex:
            self._info.print(f"获取最新交易日异常: {ex}", DyLogData.error)
            return None

    def getOneCodeDaysUnifiedWrapper(func):
        """
            @getOneCodeDaysUnified的装饰器
        """
        def wrapper(self, *args, **kwargs):
            if self._dbCache is None:
                return func(self, *args, **kwargs)

            return self._dbCache.getOneCodeDaysUnified(*args, **kwargs)

        return wrapper

    @getOneCodeDaysUnifiedWrapper
    def getOneCodeDaysUnified(self, code, dates, indicators, name=None):
        """
            获取个股日线数据的统一接口
        """
        if len(dates) == 2:
            df = self._getOneCodeDaysUnified2(code, dates[0], dates[1], indicators, name)
        else:
            df = self._getOneCodeDaysUnified3(code, dates[0], dates[1], dates[2], indicators, name)
        
        if df is not None:
            df = df.sort_index()

        return df

    def codeTDayOffsetWrapper(func):
        """
            @codeTDayOffset的装饰器
        """
        def wrapper(self, *args, **kwargs):
            if self._dbCache is None:
                return func(self, *args, **kwargs)

            return self._dbCache.codeTDayOffset(*args, **kwargs)

        return wrapper

    @codeTDayOffsetWrapper
    def codeTDayOffset(self, code, baseDate, n=0, strict=True):
        """
            获取基于个股偏移的交易日
        """
        cursor = self._findOneCodeDaysByRelative(code, baseDate, n)
        if cursor is None: return None

        df = self._getOneCodeDaysByCursor(cursor, [])
        if df is None: return None

        # 保留查找的次序,也就是说n<=0是降序,反之是升序

        if strict:
            if df.shape[0] != abs(n) + 1:
                return None

        return None if df.empty else df.index[-1].strftime("%Y-%m-%d")

    def getTicks(self, code, startDate, endDate):
        """
        获取指定股票在日期范围内的分笔数据
        @return: DataFrame
        """
        try:
            collection = self._client[self.stockTicksDb][code]
            
            start, end = self._getDateRange(startDate, endDate)
            if not start or not end:
                return None
            
            # 使用聚合管道优化查询
            pipeline = [
                {
                    '$match': {
                        'datetime': {
                            '$gte': start,
                            '$lte': end
                        }
                    }
                },
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1,
                        'price': 1,
                        'volume': 1,
                        'amount': 1,
                        'type': 1
                    }
                }
            ]
            
            # 执行聚合查询并转换为DataFrame
            cursor = collection.aggregate(pipeline)
            df = pd.DataFrame(list(cursor))
            
            if df.empty:
                return None
            
            # 设置索引
            df.set_index('datetime', inplace=True)
            
            return df
            
        except Exception as ex:
            self._info.print(f"获取股票{code}分笔数据异常: {ex}", DyLogData.error)
            return None
            
    def insertTicks(self, code, date, data):
        """
        插入分笔数据到数据库
        @param code: 股票代码
        @param date: 日期
        @param data: 分笔数据列表
        @return: bool
        """
        collection = self._client[self.stockTicksDb][code]

        try:
            # 创建索引(如果不存在)
            if 'datetime_idx' not in collection.index_information():
                collection.create_index(
                    [('datetime', pymongo.ASCENDING)],
                    unique=True,
                    background=True,
                    name='datetime_idx'
                )

            # 批量插入数据
            if data:
                # 添加写入选项
                write_options = {
                    'ordered': False,  # 无序执行提高性能
                    'bypass_document_validation': True  # 跳过文档验证提高性能
                }
                
                # 执行批量插入
                result = collection.insert_many(data, **write_options)
                
                # 添加操作结果日志
                self._info.print(f"成功插入{len(result.inserted_ids)}条Tick数据[{code},{date}]")
                
                return True

        except pymongo.errors.BulkWriteError as ex:
            # 处理重复键错误
            if 'duplicate key error' in str(ex.details):
                self._info.print(f"发现重复的Tick数据[{code},{date}]，尝试删除后重新插入", DyLogData.warning)
                
                # 删除已存在的数据
                if self._deleteTicks(code, date):
                    # 重新尝试插入
                    try:
                        collection.insert_many(data, ordered=False)
                        return True
                    except Exception as retry_ex:
                        self._info.print(f"重新插入Tick数据失败[{code},{date}]: {retry_ex}", DyLogData.error)
                        return False
            else:
                self._info.print(f"批量插入Tick数据时发生错误[{code},{date}]: {ex.details}", DyLogData.error)
                return False
            
        except Exception as ex:
            error_msg = f"插入Tick数据异常[{code},{date}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

        return True

    def isTicksExisting(self, code, date):
        cursor, count = self._findTicks(code, date, date)
        if cursor is None: return False
        if count == 0:
            return False

        return True

    def getNotExistingDates(self, code, dates, indicators):
        """
        获取指定股票在给定日期范围内不存在的指标数据日期
        @param code: 股票代码
        @param dates: 日期列表，已排序
        @param indicators: 需要检查的指标列表
        @return: {indicator: [date]} or None
        """
        if (not dates) or (not indicators):
            return None

        try:
            collection = self._getStockDaysDb()[code]
            
            # 转换日期范围
            dateStart = datetime.strptime(dates[0], '%Y-%m-%d')
            dateEnd = datetime.strptime(dates[-1] + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期范围过滤
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 投影需要的字段
                {
                    '$project': {
                        'datetime': 1,
                        **{indicator: 1 for indicator in indicators}
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline)
            
            # 初始化结果字典，假设所有日期都不存在
            data = {x: dates.copy() for x in indicators}
            
            # 处理查询结果
            for doc in cursor:
                date = doc['datetime'].strftime('%Y-%m-%d')
                
                # 检查每个指标
                for indicator in indicators:
                    if indicator in doc and indicator in data:
                        if date in data[indicator]:
                            # 移除已存在的日期
                            data[indicator].remove(date)
                            
                            # 如果某个指标的所有日期都存在，则删除该指标
                            if not data[indicator]:
                                del data[indicator]
            
            return data if data else None
        
        except Exception as ex:
            error_msg = f"查找缺失数据日期异常[{code}, {dates[0]}-{dates[-1]}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def isTradeDaysExisting(self, startDate, endDate):
        cursor, count = self._findTradeDays(startDate, endDate)
        if cursor is None: return False

        # all dates can be found in DB
        if len(DyTime.getDates(startDate, endDate)) == count:  
            return True

        return False

    def getTradeDaysByRelative(self, baseDate, n):
        """ 从数据库获取相对日期的交易日数据
            @n: 前或者向后多少个交易日
            @return: [doc of trade day]
        """
        if n > 0:
            tradeDays = self._getTradeDaysByRelativePositive(baseDate, n)
        elif n < 0:
            tradeDays = self._getTradeDaysByRelativeNegative(baseDate, n)
        else:
            tradeDays = self._getTradeDaysByRelativeZero(baseDate)

        if tradeDays is None: return None

        return tradeDays

    def getTradeDaysByAbsolute(self, startDate=None, endDate=None):
        """ 从数据库获取指定日期区间的交易日数据 """
        cursor, count = self._findTradeDays(startDate, endDate)
        if cursor is None:
            return None
    
        if startDate is not None:
            # 获取日期范围内的所有日期
            totalDates = len(DyTime.getDates(startDate, endDate))
    
            # 检查数据库中是否存在所有日期
            if totalDates != count:
                self._info.print("有些交易日[{0}, {1}]没有在数据库".format(startDate, endDate), DyLogData.error)
                return None
    
        tradeDays = []
        for d in cursor:
            if d.get('tradeDay'):  # 如果 tradeDay 为 True,表示这是一个交易日
                tradeDays.append(d)  # 使用 datetime 字段   
    
        return tradeDays
    
    def getStockCodes(self, codes=None):
        """
        获取股票代码和名称
        @param codes: 指定的股票代码列表，如果为None则获取所有股票
        @return: [{'code': code, 'name': name}, ...] or None
        """
        # 不载入任何股票
        if codes == []:
            return []
    
        try:
            collection = self._getCodeTableCollection()
            
            # 构建聚合管道
            pipeline = [
                # 1. 过滤条件
                {
                    '$match': {'code': {'$in': codes}} if codes else {}
                },
                # 2. 投影需要的字段
                {
                    '$project': {
                        '_id': 0,
                        'code': 1,
                        'name': 1
                    }
                },
                # 3. 按代码排序
                {
                    '$sort': {
                        'code': 1
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline, allowDiskUse=True)
            
            # 转换结果
            data = list(cursor)
            
            # 添加日志信息
            if data:
                self._info.print(f"获取到{len(data)}个股票代码")
            else:
                self._info.print("没有找到任何股票代码", DyLogData.warning)
                
            return data if data else None
            
        except pymongo.errors.OperationFailure as ex:
            self._info.print(f"查询股票代码表操作失败: {ex.details if hasattr(ex, 'details') else str(ex)}", 
                            DyLogData.error)
            return None
            
        except Exception as ex:
            error_msg = f"查询股票代码表异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None
    
    def getStockMarketDate(self, code, name=None):
        """
        获取个股上市日期
        由于数据库的数据限制,有可能是个股数据在数据库里的最早信息
        @param code: 股票代码
        @param name: 股票名称(用于日志)
        @return: str - 上市日期('YYYY-MM-DD')或None
        """
        try:
            collection = self._getStockDaysDb()[code]
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$lt': datetime.now()
                        }
                    }
                },
                # 2. 按日期升序排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 只取第一条记录
                {
                    '$limit': 1
                },
                # 4. 只获取日期字段
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1
                    }
                }
            ]
            
            # 执行聚合查询
            result = list(collection.aggregate(pipeline))
            
            if result:
                return result[0]['datetime'].strftime('%Y-%m-%d')
                
            return None
            
        except Exception as ex:
            error_msg = f"获取{code}:{name}上市日期异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def getSectorStockCodes(self, date, sectorCode, codes=None):
        """
        获取板块股票代码
        @param date: 日期
        @param sectorCode: 板块代码
        @param codes: 指定的股票代码列表，如果为None则获取所有股票
        @return: [{'code': code, 'name': name}, ...] or None
        """
        # 不载入任何股票
        if codes == []:
            return []

        try:
            # 获取对应日期的集合名称
            db = self._client[self.sectorCodeDbMap[sectorCode]]
            collectionNames = sorted(db.list_collection_names(include_system_collections=False))
            
            if not collectionNames:
                return []

            # 找到最近的日期集合
            for i, date_ in enumerate(collectionNames):
                if date_ > date:
                    break
            else:
                i += 1

            collection = db[collectionNames[i-1]]

            # 构建聚合管道
            pipeline = [
                # 1. 过滤条件
                {
                    '$match': {'code': {'$in': codes}} if codes else {}
                },
                # 2. 投影需要的字段
                {
                    '$project': {
                        '_id': 0,
                        'code': 1,
                        'name': 1
                    }
                },
                # 3. 按代码排序
                {
                    '$sort': {
                        'code': 1
                    }
                }
            ]

            # 执行聚合查询
            cursor = collection.aggregate(pipeline, allowDiskUse=True)
            data = list(cursor)

            # 添加日志信息
            if data:
                self._info.print(f"获取到{len(data)}个{DyStockCommon.sectors[sectorCode]}股票代码")
                
            return data if data else None

        except Exception as ex:
            error_msg = f"查询{DyStockCommon.sectors[sectorCode]}股票代码表异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def updateSectorStockCodes(self, sectorCode, date, codes):
        """
        更新板块股票代码
        @param sectorCode: 板块代码
        @param date: 日期
        @param codes: 股票代码列表
        @return: bool
        """
        try:
            collection = self._client[self.sectorCodeDbMap[sectorCode]][date]

            # 创建索引(如果不存在)
            if 'code_idx' not in collection.index_information():
                collection.create_index(
                    [('code', pymongo.ASCENDING)],
                    unique=True,
                    background=True,
                    name='code_idx'
                )

            # 准备批量更新操作
            operations = []
            for code in codes:
                operations.append(
                    pymongo.UpdateOne(
                        {'code': code['code']},
                        {'$set': {'name': code['name']}},
                        upsert=True
                    )
                )

            # 执行批量更新
            if operations:
                result = collection.bulk_write(
                    operations,
                    ordered=False,
                    bypass_document_validation=True
                )

                # 添加操作结果日志
                self._info.print(f"更新{DyStockCommon.sectors[sectorCode]}股票代码表[{date}]: "
                               f"新增{result.upserted_count}个, "
                               f"修改{result.modified_count}个")

            return True

        except pymongo.errors.BulkWriteError as ex:
            self._info.print(f"批量更新{DyStockCommon.sectors[sectorCode]}股票代码表[{date}]时发生错误: {ex.details}",
                            DyLogData.error)
            return False

        except Exception as ex:
            error_msg = f"更新{DyStockCommon.sectors[sectorCode]}股票代码表[{date}]异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

    def codeAllTradeDays(self, code, name=None):
        """
        获取股票所有的交易日期
        @param code: 股票代码
        @param name: 股票名称(用于日志)
        @return: [trade day] - 已排序的交易日列表
        """
        try:
            collection = self._getStockDaysDb()[code]
            
            # 构建聚合管道
            pipeline = [
                # 1. 投影日期字段
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1
                    }
                },
                # 2. 按日期分组去重
                {
                    '$group': {
                        '_id': {
                            '$dateToString': {
                                'format': '%Y-%m-%d',
                                'date': '$datetime'
                            }
                        }
                    }
                },
                # 3. 按日期排序
                {
                    '$sort': {
                        '_id': 1
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline, allowDiskUse=True)
            
            # 转换结果
            tradeDays = [doc['_id'] for doc in cursor]
            
            # 添加日志信息
            if tradeDays:
                self._info.print(f"获取到{code}:{name}的{len(tradeDays)}个交易日")
                
            return tradeDays if tradeDays else None
            
        except Exception as ex:
            error_msg = f"获取{code}:{name}交易日期异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    # 1分钟数据相关函数
    def getOneCodeMin1(self, code, startDate, endDate, indicators, name=None):
        """获取个股1分钟数据"""
        cursor = self._findOneCodeMin1(code, startDate, endDate, name)
        if cursor is None:
            return None

        return self._getOneCodeDaysByCursor(cursor, indicators)

    def getOneCodeMin1Unified(self, code, dates, indicators, name=None):
        """获取个股1分钟数据的统一接口"""
        if len(dates) == 2:
            df = self._getOneCodeMin1Unified2(code, dates[0], dates[1], indicators, name)
        else:
            df = self._getOneCodeMin1Unified3(code, dates[0], dates[1], dates[2], indicators, name)
        
        if df is not None:
            df = df.sort_index()

        return df

    def _findOneCodeMin1(self, code, startDate, endDate, name=None):
        """
        查找个股1分钟数据
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param name: 股票名称(用于日志)
        @return: cursor or None
        """
        try:
            # 验证输入参数
            if not code or not startDate or not endDate:
                self._info.print(f"查找1分钟数据参数无效: code={code}, startDate={startDate}, endDate={endDate}", DyLogData.error)
                return None
            
            # 检查数据库连接
            if not self._client:
                self._info.print("MongoDB客户端未连接", DyLogData.error)
                return None
            
            # 获取集合，检查集合是否存在
            db = self._client[self.stock1minDbXTquant]
            if code not in db.list_collection_names():
                self._info.print(f"股票[{code}:{name}]的1分钟数据集合不存在", DyLogData.warning)
                return None
                
            collection = db[code]
            
            # 验证日期格式并转换日期范围
            try:
                dateStart = datetime.strptime(startDate, '%Y-%m-%d')
                dateEnd = datetime.strptime(endDate + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            except ValueError as ex:
                self._info.print(f"日期格式错误[{startDate}-{endDate}]: {ex}", DyLogData.error)
                return None
            
            # 检查日期范围是否合理
            if dateStart > dateEnd:
                self._info.print(f"开始日期不能大于结束日期[{startDate} > {endDate}]", DyLogData.error)
                return None
            
            # 预检查数据量（避免查询过大的数据集）
            try:
                count = collection.count_documents({
                    'datetime': {
                        '$gte': dateStart,
                        '$lt': dateEnd
                    }
                })
                
                # 如果数据量过大，发出警告
                if count > 100000:  # 约400个交易日的数据
                    self._info.print(f"警告：{code}:{name}的查询数据量较大({count}条)，可能需要较长时间", DyLogData.warning)
                elif count == 0:
                    self._info.print(f"{code}:{name}在[{startDate}-{endDate}]期间无1分钟数据", DyLogData.warning)
                    return None
                    
            except Exception as ex:
                self._info.print(f"预检查数据量时出错: {ex}，继续执行查询", DyLogData.warning)
            
            # 构建优化的聚合管道
            pipeline = [
                # 1. 日期过滤（使用索引）
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 按时间排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 排除_id字段（减少数据传输）
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 设置聚合选项，增加容错性
            aggregate_options = {
                'allowDiskUse': True,  # 允许使用磁盘存储中间结果
                'maxTimeMS': 300000,   # 设置最大执行时间（5分钟）
                'batchSize': 1000      # 设置批量大小
            }
            
            # 执行聚合查询
            self._info.print(f"查询{code}:{name}的1分钟数据[{startDate}-{endDate}]")
            cursor = collection.aggregate(pipeline, **aggregate_options)
            
            return cursor
            
        except pymongo.errors.ServerSelectionTimeoutError as ex:
            error_msg = f"MongoDB服务器连接超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.ExecutionTimeout as ex:
            error_msg = f"查询执行超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.OperationFailure as ex:
            error_msg = f"MongoDB操作失败[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", 详细信息: {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.NetworkTimeout as ex:
            error_msg = f"网络超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except MemoryError as ex:
            error_msg = f"内存不足[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            self._info.print("建议：减少查询时间范围或增加系统内存", DyLogData.ind)
            return None
            
        except Exception as ex:
            error_msg = f"查找{code}:{name}的1分钟数据异常[{startDate}-{endDate}]: {str(ex)}"
            error_msg += f"\n异常类型: {type(ex).__name__}"
            
            # 添加更详细的错误信息
            if hasattr(ex, 'details'):
                error_msg += f"\n详细信息: {str(ex.details)}"
            if hasattr(ex, 'code'):
                error_msg += f"\n错误代码: {ex.code}"
            
            # 添加调试信息
            error_msg += f"\n调试信息:"
            error_msg += f"\n  - 数据库: {self.stock1minDbXTquant}"
            error_msg += f"\n  - 集合: {code}"
            error_msg += f"\n  - 开始时间: {startDate}"
            error_msg += f"\n  - 结束时间: {endDate}"
            
            self._info.print(error_msg, DyLogData.error)
            return None

    def getNotExistingMin1Dates(self, code, dates, indicators):
        """获取不存在的1分钟数据日期"""
        return self.getNotExistingDates(code, dates, indicators)

    def batchGetNotExistingMin1Dates(self, codes, dates, indicators):
        """
        批量获取不存在的1分钟数据日期
        @param codes: {code: name} or [code]
        @param dates: [date]
        @param indicators: [indicator]
        @return: {code: {indicator: [date]}}
        """
        try:
            result = {}
            isDict = isinstance(codes, dict)

            # 批量处理，提高效率
            for code in codes:
                name = codes[code] if isDict else None
                notExisting = self.getNotExistingMin1Dates(code, dates, indicators)
                if notExisting:
                    result[code] = notExisting

            return result if result else None

        except Exception as ex:
            self._info.print(f"批量获取1分钟数据不存在日期异常: {ex}", DyLogData.error)
            return None

    def updateMin1(self, code, data):
        """
        更新1分钟数据到数据库
        @param code: 股票代码
        @param data: [{datetime, indicator1, indicator2, ...}, ...]
        @return: bool
        """
        try:
            collection = self._client[self.stock1minDbXTquant][code]
            
            # 创建索引(如果不存在)
            if 'datetime_idx' not in collection.index_information():
                collection.create_index(
                    [('datetime', pymongo.ASCENDING)],
                    unique=True,
                    background=True,
                    name='datetime_idx'
                )

            if data:
                # 获取数据的日期范围
                dates = {record['datetime'].strftime('%Y-%m-%d') for record in data}
                dates = sorted(list(dates))

                # 如果有数据,先删除最后一个交易日的所有数据
                if dates:
                    last_date = datetime.strptime(dates[-1], '%Y-%m-%d')
                    next_date = last_date + timedelta(days=1)
                    
                    # 删除最后一个交易日的数据
                    collection.delete_many({
                        'datetime': {
                            '$gte': last_date,
                            '$lt': next_date
                        }
                    })

                    # 准备批量更新操作
                    operations = [
                        pymongo.UpdateOne(
                            {'datetime': record['datetime']},
                            {'$set': record},
                            upsert=True
                        ) for record in data
                    ]
                    
                    # 添加写入选项
                    write_options = {
                        'ordered': False,  # 无序执行提高性能
                        'bypass_document_validation': True  # 跳过文档验证
                    }
                    
                    # 执行批量写入
                    result = collection.with_options(
                        write_concern=pymongo.WriteConcern(w=1)
                    ).bulk_write(operations, **write_options)
                    
                    # 添加操作结果日志
                    self._info.print(f"更新1分钟数据[{code}]结果: "
                                   f"新增{result.upserted_count}条, "
                                   f"修改{result.modified_count}条")

            return True

        except pymongo.errors.BulkWriteError as ex:
            self._info.print(f"批量更新1分钟数据[{code}]时发生错误: {ex.details}", DyLogData.error)
            return False
            
        except Exception as ex:
            error_msg = f"更新1分钟数据[{code}]异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

    # 5分钟数据相关函数
    def getOneCodeMin5(self, code, startDate, endDate, indicators, name=None):
        """
        获取个股5分钟数据
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param indicators: 需要的指标列表
        @param name: 股票名称(用于日志)
        @return: DataFrame
        """
        cursor = self._findOneCodeMin5(code, startDate, endDate, name)
        if cursor is None:
            return None

        return self._getOneCodeDaysByCursor(cursor, indicators)

    def getOneCodeMin5Unified(self, code, dates, indicators, name=None):
        """获取个股5分钟数据的统一接口"""
        if len(dates) == 2:
            df = self._getOneCodeMin5Unified2(code, dates[0], dates[1], indicators, name)
        else:
            df = self._getOneCodeMin5Unified3(code, dates[0], dates[1], dates[2], indicators, name)
        
        if df is not None:
            df = df.sort_index()

        return df

    def _findOneCodeMin5(self, code, startDate, endDate, name=None):
        """
        查找个股5分钟数据
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param name: 股票名称(用于日志)
        @return: cursor or None
        """
        try:
            # 验证输入参数
            if not code or not startDate or not endDate:
                self._info.print(f"查找5分钟数据参数无效: code={code}, startDate={startDate}, endDate={endDate}", DyLogData.error)
                return None
            
            # 检查数据库连接
            if not self._client:
                self._info.print("MongoDB客户端未连接", DyLogData.error)
                return None
            
            # 获取集合，检查集合是否存在
            db = self._client[self.stock5minDbXTquant]
            if code not in db.list_collection_names():
                self._info.print(f"股票[{code}:{name}]的5分钟数据集合不存在", DyLogData.warning)
                return None
                
            collection = db[code]
            
            # 验证日期格式并转换日期范围
            try:
                dateStart = datetime.strptime(startDate, '%Y-%m-%d')
                dateEnd = datetime.strptime(endDate + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            except ValueError as ex:
                self._info.print(f"日期格式错误[{startDate}-{endDate}]: {ex}", DyLogData.error)
                return None
            
            # 检查日期范围是否合理
            if dateStart > dateEnd:
                self._info.print(f"开始日期不能大于结束日期[{startDate} > {endDate}]", DyLogData.error)
                return None
            
            # 预检查数据量（避免查询过大的数据集）
            try:
                count = collection.count_documents({
                    'datetime': {
                        '$gte': dateStart,
                        '$lt': dateEnd
                    }
                })
                
                # 如果数据量过大，发出警告
                if count > 100000:  # 约400个交易日的数据
                    self._info.print(f"警告：{code}:{name}的查询数据量较大({count}条)，可能需要较长时间", DyLogData.warning)
                elif count == 0:
                    self._info.print(f"{code}:{name}在[{startDate}-{endDate}]期间无5分钟数据", DyLogData.warning)
                    return None
                    
            except Exception as ex:
                self._info.print(f"预检查数据量时出错: {ex}，继续执行查询", DyLogData.warning)
            
            # 构建优化的聚合管道
            pipeline = [
                # 1. 日期过滤（使用索引）
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 按时间排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 排除_id字段（减少数据传输）
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 设置聚合选项，增加容错性
            aggregate_options = {
                'allowDiskUse': True,  # 允许使用磁盘存储中间结果
                'maxTimeMS': 300000,   # 设置最大执行时间（5分钟）
                'batchSize': 1000      # 设置批量大小
            }
            
            # 执行聚合查询
            self._info.print(f"查询{code}:{name}的5分钟数据[{startDate}-{endDate}]")
            cursor = collection.aggregate(pipeline, **aggregate_options)
            
            return cursor
            
        except pymongo.errors.ServerSelectionTimeoutError as ex:
            error_msg = f"MongoDB服务器连接超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.ExecutionTimeout as ex:
            error_msg = f"查询执行超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.OperationFailure as ex:
            error_msg = f"MongoDB操作失败[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", 详细信息: {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.NetworkTimeout as ex:
            error_msg = f"网络超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except MemoryError as ex:
            error_msg = f"内存不足[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            self._info.print("建议：减少查询时间范围或增加系统内存", DyLogData.ind)
            return None
            
        except Exception as ex:
            error_msg = f"查找{code}:{name}的5分钟数据异常[{startDate}-{endDate}]: {str(ex)}"
            error_msg += f"\n异常类型: {type(ex).__name__}"
            
            # 添加更详细的错误信息
            if hasattr(ex, 'details'):
                error_msg += f"\n详细信息: {str(ex.details)}"
            if hasattr(ex, 'code'):
                error_msg += f"\n错误代码: {ex.code}"
            
            # 添加调试信息
            error_msg += f"\n调试信息:"
            error_msg += f"\n  - 数据库: {self.stock5minDbXTquant}"
            error_msg += f"\n  - 集合: {code}"
            error_msg += f"\n  - 开始时间: {startDate}"
            error_msg += f"\n  - 结束时间: {endDate}"
            
            self._info.print(error_msg, DyLogData.error)
            return None

    def getNotExistingMin5Dates(self, code, dates, indicators):
        """
        获取不存在的5分钟数据日期
        @param code: 股票代码
        @param dates: 日期列表
        @param indicators: 需要的指标列表
        @return: {indicator: [date]} or None
        """
        return self.getNotExistingDates(code, dates, indicators)

    def batchGetNotExistingMin5Dates(self, codes, dates, indicators):
        """
        批量获取不存在的5分钟数据日期
        @param codes: {code: name} or [code]
        @param dates: [date]
        @param indicators: [indicator]
        @return: {code: {indicator: [date]}}
        """
        try:
            result = {}
            isDict = isinstance(codes, dict)

            # 批量处理，提高效率
            for code in codes:
                name = codes[code] if isDict else None
                notExisting = self.getNotExistingMin5Dates(code, dates, indicators)
                if notExisting:
                    result[code] = notExisting

            return result if result else None

        except Exception as ex:
            self._info.print(f"批量获取5分钟数据不存在日期异常: {ex}", DyLogData.error)
            return None

    def updateMin5(self, code, data):
        """
        更新5分钟数据到数据库
        @param code: 股票代码
        @param data: [{datetime, indicator1, indicator2, ...}, ...]
        @return: bool
        """
        try:
            collection = self._client[self.stock5minDbXTquant][code]
            
            # 创建索引(如果不存在)
            if 'datetime_idx' not in collection.index_information():
                collection.create_index(
                    [('datetime', pymongo.ASCENDING)],
                    unique=True,
                    background=True,
                    name='datetime_idx'
                )

            if data:
                # 获取数据的日期范围
                dates = {record['datetime'].strftime('%Y-%m-%d') for record in data}
                dates = sorted(list(dates))

                # 如果有数据,先删除最后一个交易日的所有数据
                if dates:
                    last_date = datetime.strptime(dates[-1], '%Y-%m-%d')
                    next_date = last_date + timedelta(days=1)
                    
                    # 删除最后一个交易日的数据
                    collection.delete_many({
                        'datetime': {
                            '$gte': last_date,
                            '$lt': next_date
                        }
                    })

                    # 准备批量更新操作
                    operations = [
                        pymongo.UpdateOne(
                            {'datetime': record['datetime']},
                            {'$set': record},
                            upsert=True
                        ) for record in data
                    ]
                    
                    # 添加写入选项
                    write_options = {
                        'ordered': False,  # 无序执行提高性能
                        'bypass_document_validation': True  # 跳过文档验证
                    }
                    
                    # 执行批量写入
                    result = collection.with_options(
                        write_concern=pymongo.WriteConcern(w=1)
                    ).bulk_write(operations, **write_options)
                    
                    # 添加操作结果日志
                    self._info.print(f"更新5分钟数据[{code}]结果: "
                                   f"新增{result.upserted_count}条, "
                                   f"修改{result.modified_count}条")

            return True

        except pymongo.errors.BulkWriteError as ex:
            self._info.print(f"批量更新5分钟数据[{code}]时发生错误: {ex.details}", DyLogData.error)
            return False
            
        except Exception as ex:
            error_msg = f"更新5分钟数据[{code}]异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return False

    # 财务数据相关函数
    def getOneCodeFinancialTable(self, code, startDate, endDate, indicators, name=None):
        """获取个股财务数据"""
        cursor = self._findOneCodeFinancialTable(code, startDate, endDate, name)
        if cursor is None:
            return None

        return self._getOneCodeDaysByCursor(cursor, indicators)

    def getOneCodeFinancialTableUnified(self, code, dates, indicators, name=None):
        """获取个股财务数据的统一接口"""
        if len(dates) == 2:
            df = self._getOneCodeFinancialTableUnified2(code, dates[0], dates[1], indicators, name)
        else:
            df = self._getOneCodeFinancialTableUnified3(code, dates[0], dates[1], dates[2], indicators, name)
        
        if df is not None:
            df = df.sort_index()

        return df

    def _findOneCodeFinancialTable(self, code, startDate, endDate, name=None):
        """
        查找个股财务数据
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param name: 股票名称(用于日志)
        @return: cursor or None
        """
        try:
            # 验证输入参数
            if not code or not startDate or not endDate:
                self._info.print(f"查找财务数据参数无效: code={code}, startDate={startDate}, endDate={endDate}", DyLogData.error)
                return None
            
            # 检查数据库连接
            if not self._client:
                self._info.print("MongoDB客户端未连接", DyLogData.error)
                return None
            
            # 获取集合，检查集合是否存在（默认使用资产负债表数据库）
            db = self._client[self.BalanceDbXTquant]
            if code not in db.list_collection_names():
                self._info.print(f"股票[{code}:{name}]的财务数据集合不存在", DyLogData.warning)
                return None
                
            collection = db[code]
            
            # 验证日期格式并转换日期范围
            try:
                dateStart = datetime.strptime(startDate, '%Y-%m-%d')
                dateEnd = datetime.strptime(endDate + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            except ValueError as ex:
                self._info.print(f"日期格式错误[{startDate}-{endDate}]: {ex}", DyLogData.error)
                return None
            
            # 检查日期范围是否合理
            if dateStart > dateEnd:
                self._info.print(f"开始日期不能大于结束日期[{startDate} > {endDate}]", DyLogData.error)
                return None
            
            # 预检查数据量（避免查询过大的数据集）
            try:
                count = collection.count_documents({
                    'datetime': {
                        '$gte': dateStart,
                        '$lt': dateEnd
                    }
                })
                
                # 如果数据量过大，发出警告
                if count > 10000:  # 财务数据量通常较小
                    self._info.print(f"警告：{code}:{name}的查询数据量较大({count}条)，可能需要较长时间", DyLogData.warning)
                elif count == 0:
                    self._info.print(f"{code}:{name}在[{startDate}-{endDate}]期间无财务数据", DyLogData.warning)
                    return None
                    
            except Exception as ex:
                self._info.print(f"预检查数据量时出错: {ex}，继续执行查询", DyLogData.warning)
            
            # 构建优化的聚合管道
            pipeline = [
                # 1. 日期过滤（使用索引）
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 按时间排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 排除_id字段（减少数据传输）
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 设置聚合选项，增加容错性
            aggregate_options = {
                'allowDiskUse': True,  # 允许使用磁盘存储中间结果
                'maxTimeMS': 300000,   # 设置最大执行时间（5分钟）
                'batchSize': 100       # 财务数据批量大小较小
            }
            
            # 执行聚合查询
            self._info.print(f"查询{code}:{name}的财务数据[{startDate}-{endDate}]")
            cursor = collection.aggregate(pipeline, **aggregate_options)
            
            return cursor
            
        except pymongo.errors.ServerSelectionTimeoutError as ex:
            error_msg = f"MongoDB服务器连接超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.ExecutionTimeout as ex:
            error_msg = f"查询执行超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.OperationFailure as ex:
            error_msg = f"MongoDB操作失败[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", 详细信息: {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except pymongo.errors.NetworkTimeout as ex:
            error_msg = f"网络超时[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            return None
            
        except MemoryError as ex:
            error_msg = f"内存不足[{code}:{name}][{startDate}-{endDate}]: {str(ex)}"
            self._info.print(error_msg, DyLogData.error)
            self._info.print("建议：减少查询时间范围或增加系统内存", DyLogData.ind)
            return None
            
        except Exception as ex:
            error_msg = f"查找{code}:{name}的财务数据异常[{startDate}-{endDate}]: {str(ex)}"
            error_msg += f"\n异常类型: {type(ex).__name__}"
            
            # 添加更详细的错误信息
            if hasattr(ex, 'details'):
                error_msg += f"\n详细信息: {str(ex.details)}"
            if hasattr(ex, 'code'):
                error_msg += f"\n错误代码: {ex.code}"
            
            # 添加调试信息
            error_msg += f"\n调试信息:"
            error_msg += f"\n  - 数据库: {self.BalanceDbXTquant}"
            error_msg += f"\n  - 集合: {code}"
            error_msg += f"\n  - 开始时间: {startDate}"
            error_msg += f"\n  - 结束时间: {endDate}"
            
            self._info.print(error_msg, DyLogData.error)
            return None

    def getNotExistingFinancialTableDates(self, code, dates, indicators):
        """获取不存在的财务数据日期"""
        return self.getNotExistingDates(code, dates, indicators)

    def batchGetNotExistingFinancialTableDates(self, codes, dates, indicators):
        """
        批量获取不存在的财务数据日期
        @param codes: {code: name} or [code]
        @param dates: [date]
        @param indicators: [indicator]
        @return: {code: {indicator: [date]}}
        """
        try:
            result = {}
            isDict = isinstance(codes, dict)

            # 批量处理，提高效率
            for code in codes:
                name = codes[code] if isDict else None
                notExisting = self.getNotExistingFinancialTableDates(code, dates, indicators)
                if notExisting:
                    result[code] = notExisting

            return result if result else None

        except Exception as ex:
            self._info.print(f"批量获取财务数据不存在日期异常: {ex}", DyLogData.error)
            return None

    def updateFinancialTable(self, code, table_name, df):
        """
        更新财务数据到数据库
        @code: 股票代码
        @table_name: 报表类型(如'Balance')
        @df: DataFrame格式的财务数据
        """
        try:
            # 获取对应的数据库集合
            collection = self._client[getattr(self, f"{table_name}DbXTquant")][code]

            # 创建索引(如果不存在)
            if 'datetime_idx' not in collection.index_information():
                collection.create_index([('datetime', pymongo.ASCENDING)], unique=True)

            # 转换DataFrame为MongoDB文档格式
            operations = []
            for idx, row in df.iterrows():
                record = row.to_dict()
                # 确保datetime是字符串格式
                datetime_str = idx.strftime('%Y-%m-%d %H:%M:%S.%f') if hasattr(idx, 'strftime') else str(idx)
                # 将字符串格式的datetime转换为datetime对象
                record['datetime'] = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S.%f')
                
                # 创建更新操作
                operations.append(
                    pymongo.UpdateOne(
                        {'datetime': record['datetime']},
                        {'$set': record},
                        upsert=True
                    )
                )

            # 批量更新
            if operations:
                collection.bulk_write(operations, ordered=False)

            return True

        except Exception as ex:
            self._info.print("更新财务数据[{}-{}]到MongoDB异常: {}".format(code, table_name, str(ex)), DyLogData.error)
            return False

    def getLatestFinancialDate(self):
        """获取数据库中最新的财务数据日期"""
        # 检查缓存
        if self._isCacheValid() and 'financial' in self._latestDateCache:
            return self._latestDateCache['financial']
        
        # 获取所有财务数据表对应的数据库名称
        financial_dbs = [
            self.BalanceDbXTquant,
            self.IncomeDbXTquant, 
            self.CashFlowDbXTquant,
            self.HoldernumDbXTquant
        ]
        
        latestDate = None
        try:
            # 只查询最近更新的股票
            sample_codes = ['600000', '000001', '300001']  # 使用代表性的股票代码
            
            for db_name in financial_dbs:
                db = self._client[db_name]
                
                # 只查询样本股票
                for code in sample_codes:
                    if code in db.list_collection_names():
                        collection = db[code]
                        
                        # 使用索引优化查询
                        latest = collection.find_one(
                            sort=[('datetime', -1)],
                            projection={'datetime': 1, '_id': 0}
                        )
                        
                        if latest and 'datetime' in latest:
                            date = latest['datetime']
                            if isinstance(date, datetime):
                                date = date.strftime('%Y-%m-%d')
                            if latestDate is None or date > latestDate:
                                latestDate = date
                            
            # 更新缓存
            if latestDate:
                self._updateLatestDateCache('financial', latestDate)
            
        except Exception as ex:
            self._info.print(f"获取财务数据最新日期异常: {ex}", DyLogData.error)
        
        return latestDate

    def getLatestDaysDate(self):
        """获取数据库中日线数据的最新日期"""
        # 检查缓存
        if self._isCacheValid() and 'days' in self._latestDateCache:
            return self._latestDateCache['days']
            
        db = self._getStockDaysDb()
        latestDate = None
        
        try:
            # 只查询最近添加的几个集合
            collections = list(db.list_collection_names())[-10:]  # 限制查询范围
            
            for collection_name in collections:
                if collection_name == 'default':
                    continue
                    
                collection = db[collection_name]
                # 使用索引优化查询
                latest = collection.find_one(sort=[('datetime', -1)], 
                                          projection={'datetime': 1, '_id': 0})
                
                if latest and 'datetime' in latest:
                    date = latest['datetime']
                    if isinstance(date, datetime):
                        # 确保返回YYYY-MM-DD格式的日期字符串
                        date = date.strftime('%Y-%m-%d')
                    else:
                        # 如果不是datetime对象，尝试解析日期字符串
                        try:
                            parsed_date = datetime.strptime(str(date), '%Y-%m-%d %H:%M:%S.%f')
                            date = parsed_date.strftime('%Y-%m-%d')
                        except:
                            try:
                                parsed_date = datetime.strptime(str(date), '%Y-%m-%d')
                                date = parsed_date.strftime('%Y-%m-%d')
                            except:
                                continue

                    if latestDate is None or date > latestDate:
                        latestDate = date
                        
            # 更新缓存
            if latestDate:
                self._updateLatestDateCache('days', latestDate)
                
        except Exception as ex:
            self._info.print(f"获取日线数据最新日期异常: {ex}", DyLogData.error)
            
        return latestDate

    def getLatestMin1Date(self):
        """获取数据库中1分钟线数据的最新日期"""
        if self._isCacheValid() and 'min1' in self._latestDateCache:
            return self._latestDateCache['min1']
        
        db = self._client[self.stock1minDbXTquant]
        latestDate = None
        
        try:
            # 只查询最近添加的几个集合
            collections = list(db.list_collection_names())[-10:]  # 限制查询范围
            
            for collection_name in collections:
                if collection_name == 'default':
                    continue
                    
                collection = db[collection_name]
                # 使用索引优化查询
                latest = collection.find_one(sort=[('datetime', -1)], 
                                          projection={'datetime': 1, '_id': 0})
                
                if latest and 'datetime' in latest:
                    date = latest['datetime']
                    if isinstance(date, datetime):
                        date = date.strftime('%Y-%m-%d')
                    if latestDate is None or date > latestDate:
                        latestDate = date
                        
            # 更新缓存
            if latestDate:
                self._updateLatestDateCache('min1', latestDate)
                
        except Exception as ex:
            self._info.print(f"获取1分钟线数据最新日期异常: {ex}", DyLogData.error)
            
        return latestDate

    def getLatestMin5Date(self):
        """获取数据库中5分钟线数据的最新日期"""
        if self._isCacheValid() and 'min5' in self._latestDateCache:
            return self._latestDateCache['min5']
        
        db = self._client[self.stock5minDbXTquant]
        latestDate = None
        
        try:
            # 只查询最近添加的几个集合
            collections = list(db.list_collection_names())[-10:]  # 限制查询范围
            
            for collection_name in collections:
                if collection_name == 'default':
                    continue
                    
                collection = db[collection_name]
                # 使用索引优化查询
                latest = collection.find_one(sort=[('datetime', -1)], 
                                          projection={'datetime': 1, '_id': 0})
                
                if latest and 'datetime' in latest:
                    date = latest['datetime']
                    if isinstance(date, datetime):
                        date = date.strftime('%Y-%m-%d')
                    if latestDate is None or date > latestDate:
                        latestDate = date
                        
            # 更新缓存
            if latestDate:
                self._updateLatestDateCache('min5', latestDate)
                
        except Exception as ex:
            self._info.print(f"获取5分钟线数据最新日期异常: {ex}", DyLogData.error)
            
        return latestDate

    def _updateLatestDateCache(self, db_name, date):
        """更新最新日期缓存"""
        self._latestDateCache[db_name] = date
        self._cacheExpireTime = datetime.now() + timedelta(minutes=30)  # 缓存30分钟

    def _isCacheValid(self):
        """检查缓存是否有效"""
        return (self._cacheExpireTime is not None and 
                datetime.now() < self._cacheExpireTime)

    def _createIndexes(self):
        """创建优化索引"""
        try:
            # 为日期字段创建索引
            for db_name in [self.stockDaysDbXTquant, 
                           self.stock1minDbXTquant,
                           self.stock5minDbXTquant]:
                db = self._client[db_name]
                for collection in db.list_collection_names():
                    if collection != 'default':
                        # 添加索引选项
                        db[collection].create_index(
                            [('datetime', -1)],
                            background=True,  # 后台创建索引
                            sparse=True      # 稀疏索引
                        )
                    
        except Exception as ex:
            self._info.print(f"创建索引异常: {ex}", DyLogData.warning)

    def _handleMongoError(self, ex):
        """统一的MongoDB错误处理"""
        if isinstance(ex, pymongo.errors.ServerSelectionTimeoutError):
            self._info.print("MongoDB服务器连接超时", DyLogData.error)
        elif isinstance(ex, pymongo.errors.OperationFailure):
            self._info.print(f"MongoDB操作失败: {ex.details}", DyLogData.error)
        elif isinstance(ex, pymongo.errors.WriteError):
            self._info.print(f"MongoDB写入错误: {ex.details}", DyLogData.error)
        else:
            self._info.print(f"MongoDB未知错误: {str(ex)}", DyLogData.error)

    def _convertDatetime(self, date_str):
        """统一的日期转换函数"""
        try:
            if isinstance(date_str, str):
                return datetime.strptime(date_str, '%Y-%m-%d')
            elif isinstance(date_str, datetime):
                return date_str
            else:
                raise ValueError(f"Unsupported date format: {date_str}")
        except Exception as ex:
            self._info.print(f"日期转换错误: {ex}", DyLogData.error)
            return None

    def _getDateRange(self, startDate, endDate):
        """获取规范化的日期范围"""
        start = self._convertDatetime(startDate)
        end = self._convertDatetime(endDate)
        
        if start and end:
            end = end.replace(hour=23, minute=59, second=59)
            return start, end
        return None, None

    def getExistingTicksDates(self, code, tradeDays):
        """
        批量获取指定股票在给定日期范围内已存在的分笔数据日期
        
        @param code: 股票代码
        @param tradeDays: 要检查的交易日列表
        @return: set of existing dates
        """
        try:
            # 获取集合
            collection = self._client[self.stockTicksDb][code]
            
            # 构建查询条件
            min_date = datetime.strptime(min(tradeDays), '%Y-%m-%d')
            max_date = datetime.strptime(max(tradeDays), '%Y-%m-%d') + timedelta(days=1)
            
            # 使用聚合管道进行批量查询
            pipeline = [
                {
                    '$match': {
                        'datetime': {
                            '$gte': min_date,
                            '$lt': max_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': {
                            '$dateToString': {
                                'format': '%Y-%m-%d',
                                'date': '$datetime'
                            }
                        }
                    }
                }
            ]
            
            # 执行聚合查询并返回结果集
            existing_dates = {doc['_id'] for doc in collection.aggregate(pipeline)}
            return existing_dates
            
        except Exception as ex:
            self._info.print(f"获取股票{code}的分笔数据日期异常: {ex}", DyLogData.error)
            return set()
            return set()

    def _getOneCodeMin1Unified2(self, code, startDate, endDate, indicators, name=None):
        """
        获取指定日期范围内的1分钟数据
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param indicators: 需要的指标列表
        @param name: 股票名称(用于日志)
        @return: DataFrame
        """
        try:
            collection = self._client[self.stock1minDbXTquant][code]
            
            # 转换日期范围
            dateStart = datetime.strptime(startDate, '%Y-%m-%d')
            dateEnd = datetime.strptime(endDate + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 按时间排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 字段投影
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1,
                        **{indicator: 1 for indicator in indicators}
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline, allowDiskUse=True)
            
            # 转换为DataFrame
            df = self._getOneCodeDaysByCursor(cursor, indicators)
            
            if df is None:
                self._info.print(f"没有找到{code}:{name}的1分钟数据[{startDate}-{endDate}]", DyLogData.warning)
                
            return df
            
        except Exception as ex:
            error_msg = f"获取{code}:{name}的1分钟数据异常[{startDate}-{endDate}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def _getOneCodeMin1Unified3(self, code, startDate, endDate, n, indicators, name=None):
        """
        分部分获取1分钟数据并合并
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param n: 偏移天数
        @param indicators: 需要的指标列表
        @param name: 股票名称(用于日志)
        @return: DataFrame
        """
        try:
            # 分部分载入
            # front part
            startDateNew, endDateNew = startDate, endDate
            if isinstance(startDate, int):
                startDateNew, endDateNew = endDateNew, startDateNew

            frontDf = self._getOneCodeMin1Unified2(code, startDateNew, endDateNew, indicators, name)
            if frontDf is None: 
                return None

            # back part
            backDf = self._getOneCodeMin1Unified2(code, endDate, n, indicators, name)
            if backDf is None: 
                return None

            # concat front DF and back DF
            df = pd.concat([frontDf, backDf])

            # drop duplicated
            df = df[~df.index.duplicated()]

            return df
            
        except Exception as ex:
            error_msg = f"合并{code}:{name}的1分钟数据异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def _getOneCodeMin5Unified2(self, code, startDate, endDate, indicators, name=None):
        """
        获取指定日期范围内的5分钟数据
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param indicators: 需要的指标列表
        @param name: 股票名称(用于日志)
        @return: DataFrame
        """
        try:
            collection = self._client[self.stock5minDbXTquant][code]
            
            # 转换日期范围
            dateStart = datetime.strptime(startDate, '%Y-%m-%d')
            dateEnd = datetime.strptime(endDate + ' 23:00:00', '%Y-%m-%d %H:%M:%S')
            
            # 构建聚合管道
            pipeline = [
                # 1. 日期过滤
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lt': dateEnd
                        }
                    }
                },
                # 2. 按时间排序
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                # 3. 字段投影
                {
                    '$project': {
                        '_id': 0,
                        'datetime': 1,
                        **{indicator: 1 for indicator in indicators}
                    }
                }
            ]
            
            # 执行聚合查询
            cursor = collection.aggregate(pipeline, allowDiskUse=True)
            
            # 转换为DataFrame
            df = self._getOneCodeDaysByCursor(cursor, indicators)
            
            if df is None:
                self._info.print(f"没有找到{code}:{name}的5分钟数据[{startDate}-{endDate}]", DyLogData.warning)
                
            return df
            
        except Exception as ex:
            error_msg = f"获取{code}:{name}的5分钟数据异常[{startDate}-{endDate}]: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def _getOneCodeMin5Unified3(self, code, startDate, endDate, n, indicators, name=None):
        """
        分部分获取5分钟数据并合并
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param n: 偏移天数
        @param indicators: 需要的指标列表
        @param name: 股票名称(用于日志)
        @return: DataFrame
        """
        try:
            # 分部分载入
            # front part
            startDateNew, endDateNew = startDate, endDate
            if isinstance(startDate, int):
                startDateNew, endDateNew = endDateNew, startDateNew

            frontDf = self._getOneCodeMin5Unified2(code, startDateNew, endDateNew, indicators, name)
            if frontDf is None: 
                return None

            # back part
            backDf = self._getOneCodeMin5Unified2(code, endDate, n, indicators, name)
            if backDf is None: 
                return None

            # concat front DF and back DF
            df = pd.concat([frontDf, backDf])

            # drop duplicated
            df = df[~df.index.duplicated()]

            return df
            
        except Exception as ex:
            error_msg = f"合并{code}:{name}的5分钟数据异常: {str(ex)}"
            if hasattr(ex, 'details'):
                error_msg += f", {str(ex.details)}"
            self._info.print(error_msg, DyLogData.error)
            return None

    def batchGetNotExistingMin1Dates(self, codes, tradeDays, indicators):
        """
        批量获取多个股票在指定交易日缺失的数据
        @param codes: [code]
        @param tradeDays: [trade day]
        @param indicators: [indicator]
        @return: {code: {indicator: [trade day]}}
        """
        try:
            # 转换日期范围
            start_date = datetime.strptime(min(tradeDays), '%Y-%m-%d')
            end_date = datetime.strptime(max(tradeDays), '%Y-%m-%d')
            
            # 构建聚合管道
            pipeline = [
                # 1. 匹配指定的股票代码和日期范围
                {
                    '$match': {
                        'code': {'$in': codes},
                        'datetime': {
                            '$gte': start_date,
                            '$lt': end_date + timedelta(days=1)
                        }
                    }
                },
                # 2. 按股票分组
                {
                    '$group': {
                        '_id': '$code',
                        'dates': {'$addToSet': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$datetime'}}},
                        'indicators': {'$addToSet': '$indicator'}
                    }
                }
            ]
            
            # 执行聚合查询
            results = {}
            for code in codes:
                collection = self._client[self.stock1minDbXTquant][code]
                
                # 检查并处理索引
                try:
                    # 先检查是否已经存在datetime索引
                    existing_indexes = collection.index_information()
                    datetime_index_exists = False
                    
                    # 检查所有现有索引
                    for index_name, index_info in existing_indexes.items():
                        if len(index_info['key']) == 1 and index_info['key'][0][0] == 'datetime':
                            datetime_index_exists = True
                            break
                    
                    # 如果没有datetime索引，创建一个
                    if not datetime_index_exists:
                        collection.create_index([('datetime', 1)], name='datetime_1')
                        
                except Exception as ex:
                    self._info.print(f"处理索引时出错[{code}]: {ex}", DyLogData.warning)
                    # 继续处理，不中断流程
                
                # 获取该股票的现有数据
                cursor = collection.aggregate(pipeline)
                existing_data = list(cursor)
                
                # 分析缺失的日期和指标
                if not existing_data:
                    # 该股票所有数据都需要更新
                    results[code] = {
                        indicator: tradeDays.copy() 
                        for indicator in indicators
                    }
                else:
                    existing = existing_data[0]
                    missing_dates = set(tradeDays) - set(existing.get('dates', []))
                    missing_indicators = set(indicators) - set(existing.get('indicators', []))
                    
                    if missing_dates or missing_indicators:
                        results[code] = {}
                        # 对缺失的指标，需要所有日期的数据
                        for indicator in missing_indicators:
                            results[code][indicator] = tradeDays.copy()
                        # 对现有指标，只需要缺失日期的数据
                        for indicator in set(indicators) - missing_indicators:
                            if missing_dates:
                                results[code][indicator] = list(missing_dates)
            
            return results
            
        except Exception as ex:
            self._info.print(f"批量查询1分钟数据异常: {ex}", DyLogData.error)
            return None
    def batchUpdateMin1(self, data):
        """
        批量更新1分钟数据到MongoDB
        @param data: {code: [{'datetime': value, 'indicator': value, ...}, ...]}
        @return: True/False
        """
        BATCH_SIZE = 5000  # 每批写入记录数
        MAX_WORKERS = 4    # 并行写入的线程数
        
        def process_batch(code, records):
            """处理单个股票的数据写入"""
            try:
                # 获取集合
                collection = self._client[self.stock1minDbXTquant][code]
                
                # 确保索引存在
                if 'datetime_1' not in collection.index_information():
                    collection.create_index([('datetime', 1)], unique=True)
                
                # 获取最后交易日期
                last_record = collection.find_one(sort=[('datetime', -1)])
                if last_record:
                    last_date = last_record['datetime'].strftime('%Y-%m-%d')
                    
                    # 删除最后一个交易日的数据(避免重复)
                    collection.delete_many({
                        'datetime': {
                            '$gte': datetime.strptime(f"{last_date} 00:00:00.000", '%Y-%m-%d %H:%M:%S.%f'),
                            '$lt': datetime.strptime(f"{last_date} 23:59:59.999", '%Y-%m-%d %H:%M:%S.%f')
                        }
                    })
                
                # 按批次写入数据
                for i in range(0, len(records), BATCH_SIZE):
                    batch = records[i:i + BATCH_SIZE]
                    operations = [
                        UpdateOne(
                            {'datetime': record['datetime']},
                            {'$set': record},
                            upsert=True
                        ) for record in batch
                    ]
                    
                    collection.bulk_write(
                        operations,
                        ordered=False,
                        bypass_document_validation=True
                    )
                
                return True, code, len(records)
                
            except Exception as ex:
                return False, code, str(ex)
        
        try:
            if not data:
                self._info.print("没有数据需要更新", DyLogData.warning)
                return False
                
            start_time = time.time()
            success_count = 0
            total_records = 0
            
            # 并行处理数据写入
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                # 提交所有写入任务
                future_to_code = {
                    executor.submit(process_batch, code, records): code
                    for code, records in data.items()
                }
                
                # 获取结果
                for future in concurrent.futures.as_completed(future_to_code):
                    success, code, result = future.result()
                    if success:
                        success_count += 1
                        total_records += result
                        self._info.print(f"股票{code}更新成功: {result}条记录", DyLogData.ind)
                    else:
                        self._info.print(f"股票{code}更新失败: {result}", DyLogData.error)
            
            # 打印统计信息
            end_time = time.time()
            duration = end_time - start_time
            self._info.print(
                "批量更新统计:\n"
                "- 总股票数: {}\n"
                "- 成功更新: {}\n"
                "- 总记录数: {}\n"
                "- 总耗时: {:.2f}秒\n"
                "- 平均每股耗时: {:.2f}秒\n"
                "- 成功率: {:.2f}%".format(
                    len(data),
                    success_count,
                    total_records,
                    duration,
                    duration/len(data),
                    success_count/len(data)*100
                ),
                DyLogData.ind
            )
            
            return success_count > 0
            
        except Exception as ex:
            self._info.print(f"批量更新1分钟数据异常: {ex}", DyLogData.error)
            return False

    def batchGetNotExistingMin5Dates(self, codes, tradeDays, indicators):
        """
        批量获取多个股票在指定交易日缺失的5分钟数据
        @param codes: [code]
        @param tradeDays: [trade day]
        @param indicators: [indicator]
        @return: {code: {indicator: [trade day]}}
        """
        try:
            # 转换日期范围
            start_date = datetime.strptime(min(tradeDays), '%Y-%m-%d')
            end_date = datetime.strptime(max(tradeDays), '%Y-%m-%d')
            
            # 构建聚合管道
            pipeline = [
                {
                    '$match': {
                        'code': {'$in': codes},
                        'datetime': {
                            '$gte': start_date,
                            '$lt': end_date + timedelta(days=1)
                        }
                    }
                },
                {
                    '$group': {
                        '_id': '$code',
                        'dates': {'$addToSet': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$datetime'}}},
                        'indicators': {'$addToSet': '$indicator'}
                    }
                }
            ]
            
            results = {}
            for code in codes:
                collection = self._client[self.stock5minDbXTquant][code]
                
                # 检查并处理索引
                try:
                    existing_indexes = collection.index_information()
                    datetime_index_exists = False
                    for index_name, index_info in existing_indexes.items():
                        if len(index_info['key']) == 1 and index_info['key'][0][0] == 'datetime':
                            datetime_index_exists = True
                            break
                    
                    if not datetime_index_exists:
                        collection.create_index([('datetime', 1)], name='datetime_1')
                except Exception as ex:
                    self._info.print(f"处理索引时出错[{code}]: {ex}", DyLogData.warning)
                
                cursor = collection.aggregate(pipeline)
                existing_data = list(cursor)
                
                if not existing_data:
                    results[code] = {indicator: tradeDays.copy() for indicator in indicators}
                else:
                    existing = existing_data[0]
                    missing_dates = set(tradeDays) - set(existing.get('dates', []))
                    missing_indicators = set(indicators) - set(existing.get('indicators', []))
                    
                    if missing_dates or missing_indicators:
                        results[code] = {}
                        for indicator in missing_indicators:
                            results[code][indicator] = tradeDays.copy()
                        for indicator in set(indicators) - missing_indicators:
                            if missing_dates:
                                results[code][indicator] = list(missing_dates)
        
            return results
            
        except Exception as ex:
            self._info.print(f"批量查询5分钟数据异常: {ex}", DyLogData.error)
            return None

    def batchGetNotExistingDaysDates(self, codes, tradeDays, indicators):
        """
        批量获取多个股票在指定交易日缺失的日线数据
        @param codes: [code]
        @param tradeDays: [trade day]
        @param indicators: [indicator]
        @return: {code: {indicator: [trade day]}}
        """
        try:
            # 转换日期范围
            start_date = datetime.strptime(min(tradeDays), '%Y-%m-%d')
            end_date = datetime.strptime(max(tradeDays), '%Y-%m-%d')
            
            pipeline = [
                {
                    '$match': {
                        'code': {'$in': codes},
                        'datetime': {
                            '$gte': start_date,
                            '$lt': end_date + timedelta(days=1)
                        }
                    }
                },
                {
                    '$group': {
                        '_id': '$code',
                        'dates': {'$addToSet': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$datetime'}}},
                        'indicators': {'$addToSet': '$indicator'}
                    }
                }
            ]
            
            results = {}
            for code in codes:
                collection = self._client[self.stockDaysDbXTquant][code]
                
                try:
                    existing_indexes = collection.index_information()
                    datetime_index_exists = False
                    for index_name, index_info in existing_indexes.items():
                        if len(index_info['key']) == 1 and index_info['key'][0][0] == 'datetime':
                            datetime_index_exists = True
                            break
                    
                    if not datetime_index_exists:
                        collection.create_index([('datetime', 1)], name='datetime_1')
                except Exception as ex:
                    self._info.print(f"处理索引时出错[{code}]: {ex}", DyLogData.warning)
                
                cursor = collection.aggregate(pipeline)
                existing_data = list(cursor)
                
                if not existing_data:
                    results[code] = {indicator: tradeDays.copy() for indicator in indicators}
                else:
                    existing = existing_data[0]
                    missing_dates = set(tradeDays) - set(existing.get('dates', []))
                    missing_indicators = set(indicators) - set(existing.get('indicators', []))
                    
                    if missing_dates or missing_indicators:
                        results[code] = {}
                        for indicator in missing_indicators:
                            results[code][indicator] = tradeDays.copy()
                        for indicator in set(indicators) - missing_indicators:
                            if missing_dates:
                                results[code][indicator] = list(missing_dates)
        
            return results
            
        except Exception as ex:
            self._info.print(f"批量查询日线数据异常: {ex}", DyLogData.error)
            return None

    def batchGetNotExistingTicksDates(self, codes, tradeDays, indicators):
        """
        批量获取多个股票在指定交易日缺失的分笔数据
        @param codes: [code]
        @param tradeDays: [trade day]
        @param indicators: [indicator]
        @return: {code: {indicator: [trade day]}}
        """
        try:
            start_date = datetime.strptime(min(tradeDays), '%Y-%m-%d')
            end_date = datetime.strptime(max(tradeDays), '%Y-%m-%d')
            
            pipeline = [
                {
                    '$match': {
                        'code': {'$in': codes},
                        'datetime': {
                            '$gte': start_date,
                            '$lt': end_date + timedelta(days=1)
                        }
                    }
                },
                {
                    '$group': {
                        '_id': '$code',
                        'dates': {'$addToSet': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$datetime'}}},
                        'indicators': {'$addToSet': '$indicator'}
                    }
                }
            ]
            
            results = {}
            for code in codes:
                collection = self._client[self.stockTicksDb][code]
                
                try:
                    existing_indexes = collection.index_information()
                    datetime_index_exists = False
                    for index_name, index_info in existing_indexes.items():
                        if len(index_info['key']) == 1 and index_info['key'][0][0] == 'datetime':
                            datetime_index_exists = True
                            break
                    
                    if not datetime_index_exists:
                        collection.create_index([('datetime', 1)], name='datetime_1')
                except Exception as ex:
                    self._info.print(f"处理索引时出错[{code}]: {ex}", DyLogData.warning)
                
                cursor = collection.aggregate(pipeline)
                existing_data = list(cursor)
                
                if not existing_data:
                    results[code] = {indicator: tradeDays.copy() for indicator in indicators}
                else:
                    existing = existing_data[0]
                    missing_dates = set(tradeDays) - set(existing.get('dates', []))
                    missing_indicators = set(indicators) - set(existing.get('indicators', []))
                    
                    if missing_dates or missing_indicators:
                        results[code] = {}
                        for indicator in missing_indicators:
                            results[code][indicator] = tradeDays.copy()
                        for indicator in set(indicators) - missing_indicators:
                            if missing_dates:
                                results[code][indicator] = list(missing_dates)
            
            return results
            
        except Exception as ex:
            self._info.print(f"批量查询分笔数据异常: {ex}", DyLogData.error)
            return None

    def batchGetNotExistingFinancialDates(self, codes, tradeDays, tables):
        """
        批量获取多个股票在指定交易日缺失的财务数据
        @param codes: [code]
        @param tradeDays: [trade day]
        @param tables: [table_name] 需要查询的财务报表类型
        @return: {code: {table: [trade day]}}
        """
        try:
            start_date = datetime.strptime(min(tradeDays), '%Y-%m-%d')
            end_date = datetime.strptime(max(tradeDays), '%Y-%m-%d')
            
            results = {}
            for code in codes:
                results[code] = {}
                
                for table in tables:
                    db_name = getattr(self, f"{table}XTquant")
                    collection = self._client[db_name][code]
                    
                    try:
                        existing_indexes = collection.index_information()
                        datetime_index_exists = False
                        for index_name, index_info in existing_indexes.items():
                            if len(index_info['key']) == 1 and index_info['key'][0][0] == 'datetime':
                                datetime_index_exists = True
                                break
                        
                        if not datetime_index_exists:
                            collection.create_index([('datetime', 1)], name='datetime_1')
                    except Exception as ex:
                        self._info.print(f"处理索引时出错[{code}-{table}]: {ex}", DyLogData.warning)
                    
                    # 查询现有数据日期
                    existing_dates = set(
                        d.strftime('%Y-%m-%d') 
                        for d in collection.distinct(
                            'datetime',
                            {
                                'datetime': {
                                    '$gte': start_date,
                                    '$lt': end_date + timedelta(days=1)
                                }
                            }
                        )
                    )
                    
                    # 计算缺失日期
                    missing_dates = set(tradeDays) - existing_dates
                    if missing_dates:
                        results[code][table] = list(missing_dates)
                
                # 如果该股票所有表都没有缺失数据，则从结果中删除
                if not results[code]:
                    del results[code]
            
            return results
            
        except Exception as ex:
            self._info.print(f"批量查询财务数据异常: {ex}", DyLogData.error)
            return None

    def _createSectorCollections(self):
        """创建板块数据集合（旧方法，保持兼容性）"""
        # 创建板块信息集合 - 修复：使用正确的数据库
        self.sectorCollection = self._client[self.stockCommonDbXTquant]['sectorData']
        # 创建板块成分股集合 - 修复：使用正确的数据库
        self.sectorStocksCollection = self._client[self.stockCommonDbXTquant]['sectorStocks']
        
        # 确保索引
        self.sectorCollection.create_index([('type', pymongo.ASCENDING), ('code', pymongo.ASCENDING)], unique=True)
        self.sectorStocksCollection.create_index([('sector_code', pymongo.ASCENDING), ('datetime', pymongo.ASCENDING)], unique=True)

    def updateSectorData(self, sectors, date):
        """
        更新板块数据
        @param sectors: {sector_type: {sector_code: sector_name}}
        @param date: 更新日期，格式YYYY-MM-DD
        """
        try:
            operations = []
            for sector_type, sector_dict in sectors.items():
                for code, name in sector_dict.items():
                    operations.append(
                        pymongo.UpdateOne(
                            {'type': sector_type, 'code': code},
                            {'$set': {
                                'name': name,
                                'lastUpdate': datetime.strptime(date, '%Y-%m-%d')
                            }},
                            upsert=True
                        )
                    )
            
            if operations:
                self.sectorCollection.bulk_write(operations, ordered=False)
                return True
                
            return False
            
        except Exception as ex:
            self._info.print(f"更新板块数据到数据库异常: {ex}", DyLogData.error)
            return False

    def updateSectorStocks(self, sector_stocks, date):
        """
        更新板块成分股数据
        @param sector_stocks: {sector_code: {stock_code: stock_name}}
        @param date: 更新日期，格式YYYY-MM-DD
        """
        try:
            operations = []
            dt = datetime.strptime(date, '%Y-%m-%d')
            
            for sector_code, stocks in sector_stocks.items():
                operations.append(
                    pymongo.UpdateOne(
                        {'sector_code': sector_code, 'datetime': dt},
                        {'$set': {
                            'stocks': stocks,
                            'lastUpdate': dt
                        }},
                        upsert=True
                    )
                )
            
            if operations:
                self.sectorStocksCollection.bulk_write(operations, ordered=False)
                return True
                
            return False
            
        except Exception as ex:
            self._info.print(f"更新板块成分股到数据库异常: {ex}", DyLogData.error)
            return False

    def get_sector_by_name(self, sector_name):
        """
        通过板块名称查询板块信息
        @return: {type: 板块类型, code: 板块代码} 或 None
        """
        try:
            result = self.sectorCollection.find_one({'name': sector_name})
            if result:
                return {'type': result['type'], 'code': result['code']}
            return None
        except Exception as ex:
            self._info.print(f"查询板块信息异常: {ex}", DyLogData.error)
            return None

    def get_sector_stocks(self, sector_code, date):
        """
        获取指定板块在指定日期的成分股
        @return: {stock_code: stock_name} 或 None
        """
        try:
            # 获取最新日期数据
            latest = self.sectorStocksCollection.find_one(
                {'sector_code': sector_code},
                sort=[('datetime', pymongo.DESCENDING)]
            )
            return latest['stocks'] if latest else None
        except Exception as ex:
            self._info.print(f"查询板块成分股异常: {ex}", DyLogData.error)
            return None

    def get_sector_stocks(self, sector_code, date):
        """
        获取板块股票列表
        """
        try:
            collection = self._client[self.stockCommonDbXTquant]['sectors']
            
            # 查询指定日期的板块股票
            doc = collection.find_one({
                'date': date,
                'code': sector_code
            })
            
            if doc:
                return doc.get('stocks', [])
            return []
            
        except Exception as ex:
            self._info.print(f"获取板块股票异常: {ex}", DyLogData.error)
            return []

    def updateDownsampleData(self, code, period, df):
        """
        更新降采样数据
        @param code: 股票代码
        @param period: 降采样周期（分钟）
        @param df: 降采样后的DataFrame
        @return: bool
        """
        try:
            # 获取数据库
            db = self._client[self.stockDownsampleDbXTquant]
            
            # 获取集合名称：使用周期作为集合名的一部分
            collection_name = f"min{period}_{code}"
            collection = db[collection_name]
            
            # 创建索引
            collection.create_index([('datetime', 1)], unique=True)
            
            # 转换数据格式
            data = df.reset_index().to_dict('records')
            
            # 批量更新
            if data:
                operations = []
                for record in data:
                    operations.append(
                        pymongo.UpdateOne(
                            {'datetime': record['datetime']},
                            {'$set': record},
                            upsert=True
                        )
                    )
                
                if operations:
                    collection.bulk_write(operations, ordered=False)
                    
            return True
            
        except Exception as ex:
            self._info.print(f"更新降采样数据异常[{code}-{period}分钟]: {ex}", DyLogData.error)
            return False
            
    def getLatestDownsampleDate(self, period):
        """
        获取降采样数据的最新日期
        @param period: 降采样周期（分钟）
        @return: 最新日期字符串或None
        """
        try:
            db = self._client[self.stockDownsampleDbXTquant]
            
            # 获取所有集合名称
            collections = db.list_collection_names()
            
            # 筛选指定周期的集合
            prefix = f"min{period}_"
            period_collections = [c for c in collections if c.startswith(prefix)]
            
            if not period_collections:
                return None
                
            latest_date = None
            for collection_name in period_collections:
                collection = db[collection_name]
                
                # 获取该集合的最新日期
                result = collection.find_one(
                    {},
                    sort=[('datetime', -1)]
                )
                
                if result and 'datetime' in result:
                    date_str = result['datetime'].strftime('%Y-%m-%d')
                    if latest_date is None or date_str > latest_date:
                        latest_date = date_str
                        
            return latest_date
            
        except Exception as ex:
            self._info.print(f"获取降采样最新日期异常[{period}分钟]: {ex}", DyLogData.error)
            return None
            
    def getMin1EarliestDate(self):
        """
        获取1分钟数据的最早日期
        @return: 最早日期字符串或None
        """
        try:
            db = self._client[self.stock1minDbXTquant]
            
            # 获取所有股票集合
            collections = db.list_collection_names()
            
            earliest_date = None
            for code in collections:
                if code == 'default':  # 跳过默认集合
                    continue
                    
                collection = db[code]
                
                # 获取该股票的最早日期
                result = collection.find_one(
                    {},
                    sort=[('datetime', 1)]
                )
                
                if result and 'datetime' in result:
                    date_str = result['datetime'].strftime('%Y-%m-%d')
                    if earliest_date is None or date_str < earliest_date:
                        earliest_date = date_str
                        
            return earliest_date
            
        except Exception as ex:
            self._info.print(f"获取1分钟最早日期异常: {ex}", DyLogData.error)
            return None
            
    def getOneCodeDownsample(self, code, period, startDate, endDate, indicators, name=None):
        """
        获取单个股票的降采样数据
        @param code: 股票代码
        @param period: 降采样周期（分钟）
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param indicators: 指标列表
        @param name: 股票名称
        @return: DataFrame或None
        """
        try:
            collection_name = f"min{period}_{code}"
            collection = self._client[self.stockDownsampleDbXTquant][collection_name]
            
            # 转换日期
            dateStart = datetime.strptime(startDate, '%Y-%m-%d')
            dateEnd = datetime.strptime(endDate + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            
            # 构建查询
            pipeline = [
                {
                    '$match': {
                        'datetime': {
                            '$gte': dateStart,
                            '$lte': dateEnd
                        }
                    }
                },
                {
                    '$sort': {
                        'datetime': 1
                    }
                },
                {
                    '$project': {
                        '_id': 0
                    }
                }
            ]
            
            # 执行查询
            cursor = collection.aggregate(pipeline)
            
            # 转换为DataFrame
            df = pd.DataFrame(list(cursor))
            if not df.empty:
                df.set_index('datetime', inplace=True)
                # 只返回需要的列
                if indicators:
                    df = df[indicators]
                    
            return df
            
        except Exception as ex:
            self._info.print(f"获取降采样数据异常[{code}-{period}分钟]: {ex}", DyLogData.error)
            return None
            
    def getDownsampleLatestDate(self, period):
        """
        获取指定周期降采样数据的最新日期
        @param period: 降采样周期（分钟）
        @return: 最新日期字符串或None
        """
        return self.getLatestDownsampleDate(period)

    # ======================== 板块数据管理方法 ========================
    
    def updateSectorDataNew(self, sectors_data, date):
        """
        更新板块基本信息数据
        @param sectors_data: {sector_type: {sector_code: sector_name}}
        @param date: 更新日期，格式YYYY-MM-DD
        @return: bool
        """
        try:
            db = self._client[self.stockSectorDbXTquant]
            
            for sector_type, sectors in sectors_data.items():
                collection_name = f"sector_{sector_type}"
                collection = db[collection_name]
                
                # 创建索引
                collection.create_index([('date', -1)])
                collection.create_index([('sector_code', 1)])
                collection.create_index([('date', -1), ('sector_code', 1)])
                
                # 删除指定日期的旧数据
                collection.delete_many({'date': date})
                
                # 插入新数据
                if sectors:
                    documents = []
                    for sector_code, sector_name in sectors.items():
                        documents.append({
                            'date': date,
                            'sector_code': sector_code,
                            'sector_name': sector_name,
                            'sector_type': sector_type,
                            'update_time': datetime.now()
                        })
                    
                    collection.insert_many(documents)
                    self._info.print(f"更新{sector_type}板块数据: {len(documents)}条记录")
            
            return True
            
        except Exception as ex:
            self._info.print(f"更新板块数据异常: {ex}", DyLogData.error)
            return False
    
    def updateSectorStocksNew(self, sector_stocks_data, date):
        """
        更新板块成分股数据
        @param sector_stocks_data: {sector_code: {stock_code: stock_name}}
        @param date: 更新日期，格式YYYY-MM-DD
        @return: bool
        """
        try:
            db = self._client[self.stockSectorStocksDbXTquant]
            collection = db['sector_stocks']
            
            # 创建索引
            collection.create_index([('date', -1)])
            collection.create_index([('sector_code', 1)])
            collection.create_index([('date', -1), ('sector_code', 1)])
            
            # 删除指定日期的旧数据
            collection.delete_many({'date': date})
            
            # 插入新数据
            total_records = 0
            if sector_stocks_data:
                documents = []
                for sector_code, stocks in sector_stocks_data.items():
                    if stocks:
                        stock_list = []
                        for stock_code, stock_name in stocks.items():
                            stock_list.append({
                                'stock_code': stock_code,
                                'stock_name': stock_name
                            })
                        
                        documents.append({
                            'date': date,
                            'sector_code': sector_code,
                            'stocks': stock_list,
                            'stock_count': len(stock_list),
                            'update_time': datetime.now()
                        })
                        total_records += len(stock_list)
                
                if documents:
                    collection.insert_many(documents)
                    self._info.print(f"更新板块成分股数据: {len(documents)}个板块, {total_records}只股票")
            
            return True
            
        except Exception as ex:
            self._info.print(f"更新板块成分股数据异常: {ex}", DyLogData.error)
            return False
    
    def getSectorData(self, date, sector_type=None):
        """
        获取指定日期的板块数据
        @param date: 日期，格式YYYY-MM-DD
        @param sector_type: 板块类型，None表示获取所有类型
        @return: {sector_type: {sector_code: sector_name}} or None
        """
        try:
            db = self._client[self.stockSectorDbXTquant]
            result = {}
            
            if sector_type:
                # 获取指定类型的板块数据
                collection_name = f"sector_{sector_type}"
                if collection_name in db.list_collection_names():
                    collection = db[collection_name]
                    cursor = collection.find({'date': date})
                    
                    sectors = {}
                    for doc in cursor:
                        sectors[doc['sector_code']] = doc['sector_name']
                    
                    if sectors:
                        result[sector_type] = sectors
            else:
                # 获取所有类型的板块数据
                collections = db.list_collection_names()
                sector_collections = [c for c in collections if c.startswith('sector_')]
                
                for collection_name in sector_collections:
                    sector_type_name = collection_name.replace('sector_', '')
                    collection = db[collection_name]
                    cursor = collection.find({'date': date})
                    
                    sectors = {}
                    for doc in cursor:
                        sectors[doc['sector_code']] = doc['sector_name']
                    
                    if sectors:
                        result[sector_type_name] = sectors
            
            return result if result else None
            
        except Exception as ex:
            self._info.print(f"获取板块数据异常: {ex}", DyLogData.error)
            return None
    
    def getSectorStocksData(self, date, sector_code=None):
        """
        获取指定日期的板块成分股数据
        @param date: 日期，格式YYYY-MM-DD
        @param sector_code: 板块代码，None表示获取所有板块
        @return: {sector_code: [{'stock_code': xxx, 'stock_name': xxx}]} or None
        """
        try:
            db = self._client[self.stockSectorStocksDbXTquant]
            collection = db['sector_stocks']
            
            # 构建查询条件
            query = {'date': date}
            if sector_code:
                query['sector_code'] = sector_code
            
            cursor = collection.find(query)
            
            result = {}
            for doc in cursor:
                result[doc['sector_code']] = doc['stocks']
            
            return result if result else None
            
        except Exception as ex:
            self._info.print(f"获取板块成分股数据异常: {ex}", DyLogData.error)
            return None
    
    def getLatestSectorDate(self):
        """
        获取板块数据的最新日期
        @return: 最新日期字符串或None
        """
        try:
            db = self._client[self.stockSectorDbXTquant]
            collections = db.list_collection_names()
            sector_collections = [c for c in collections if c.startswith('sector_')]
            
            latest_date = None
            for collection_name in sector_collections:
                collection = db[collection_name]
                result = collection.find_one({}, sort=[('date', -1)])
                
                if result and 'date' in result:
                    date_str = result['date']
                    if latest_date is None or date_str > latest_date:
                        latest_date = date_str
            
            return latest_date
            
        except Exception as ex:
            self._info.print(f"获取板块最新日期异常: {ex}", DyLogData.error)
            return None
    
    def getSectorsByStock(self, stock_code, date):
        """
        获取指定股票所属的板块
        @param stock_code: 股票代码
        @param date: 日期，格式YYYY-MM-DD
        @return: [sector_code] or []
        """
        try:
            db = self._client[self.stockSectorStocksDbXTquant]
            collection = db['sector_stocks']
            
            # 查询包含该股票的板块
            cursor = collection.find({
                'date': date,
                'stocks.stock_code': stock_code
            })
            
            sectors = []
            for doc in cursor:
                sectors.append(doc['sector_code'])
            
            return sectors
            
        except Exception as ex:
            self._info.print(f"获取股票所属板块异常: {ex}", DyLogData.error)
            return []

    def checkDatabaseHealth(self):
        """
        检查数据库健康状态
        @return: dict 包含检查结果的字典
        """
        health_status = {
            'connection': False,
            'indexes': False,
            'collections': 0,
            'issues': []
        }
        
        try:
            # 检查数据库连接
            if self._client:
                # 测试连接
                self._client.admin.command('ping')
                health_status['connection'] = True
                self._info.print("✓ MongoDB连接正常", DyLogData.ind)
            else:
                health_status['issues'].append("MongoDB客户端未连接")
                return health_status
            
            # 检查1分钟数据库
            db = self._client[self.stock1minDbXTquant]
            collections = db.list_collection_names()
            health_status['collections'] = len(collections)
            
            if collections:
                self._info.print(f"✓ 1分钟数据库包含{len(collections)}个集合", DyLogData.ind)
                
                # 抽样检查索引
                sample_collections = collections[:min(5, len(collections))]
                missing_indexes = []
                
                for collection_name in sample_collections:
                    collection = db[collection_name]
                    indexes = collection.index_information()
                    
                    # 检查datetime索引
                    datetime_index_exists = any(
                        'datetime' in idx.get('key', [{}])[0] 
                        for idx in indexes.values() 
                        if isinstance(idx.get('key'), list) and idx['key']
                    )
                    
                    if not datetime_index_exists:
                        missing_indexes.append(collection_name)
                
                if missing_indexes:
                    health_status['issues'].append(f"缺少datetime索引的集合: {missing_indexes}")
                else:
                    health_status['indexes'] = True
                    self._info.print("✓ 索引检查正常", DyLogData.ind)
            else:
                health_status['issues'].append("1分钟数据库为空")
            
        except Exception as ex:
            health_status['issues'].append(f"健康检查异常: {str(ex)}")
            self._info.print(f"数据库健康检查异常: {ex}", DyLogData.error)
        
        return health_status
    
    def optimizeDatabase(self, code_list=None):
        """
        优化数据库性能
        @param code_list: 要优化的股票代码列表，None表示全部
        """
        try:
            self._info.print("开始数据库优化...", DyLogData.ind)
            
            db = self._client[self.stock1minDbXTquant]
            
            if code_list is None:
                collections = db.list_collection_names()
            else:
                collections = [code for code in code_list if code in db.list_collection_names()]
            
            if not collections:
                self._info.print("没有需要优化的集合", DyLogData.warning)
                return
            
            optimized_count = 0
            
            for collection_name in collections:
                try:
                    collection = db[collection_name]
                    
                    # 检查并创建datetime索引
                    indexes = collection.index_information()
                    datetime_index_exists = any(
                        'datetime' in str(idx.get('key', ''))
                        for idx in indexes.values()
                    )
                    
                    if not datetime_index_exists:
                        collection.create_index(
                            [('datetime', pymongo.ASCENDING)],
                            background=True,
                            name='datetime_idx'
                        )
                        self._info.print(f"为{collection_name}创建datetime索引", DyLogData.ind)
                    
                    # 压缩集合（可选，可能耗时较长）
                    # db.command("compact", collection_name)
                    
                    optimized_count += 1
                    
                except Exception as ex:
                    self._info.print(f"优化集合{collection_name}时出错: {ex}", DyLogData.warning)
            
            self._info.print(f"数据库优化完成，处理了{optimized_count}个集合", DyLogData.ind)
            
        except Exception as ex:
            self._info.print(f"数据库优化异常: {ex}", DyLogData.error)
    
    def getCollectionStats(self, code):
        """
        获取集合统计信息
        @param code: 股票代码
        @return: dict 统计信息
        """
        try:
            db = self._client[self.stock1minDbXTquant]
            
            if code not in db.list_collection_names():
                return None
            
            collection = db[code]
            stats = db.command("collStats", code)
            
            # 获取文档数量
            doc_count = collection.count_documents({})
            
            # 获取日期范围
            earliest_doc = collection.find().sort("datetime", 1).limit(1)
            latest_doc = collection.find().sort("datetime", -1).limit(1)
            
            earliest_date = None
            latest_date = None
            
            earliest_list = list(earliest_doc)
            latest_list = list(latest_doc)
            
            if earliest_list:
                earliest_date = earliest_list[0].get('datetime')
            if latest_list:
                latest_date = latest_list[0].get('datetime')
            
            return {
                'code': code,
                'document_count': doc_count,
                'size_bytes': stats.get('size', 0),
                'avg_obj_size': stats.get('avgObjSize', 0),
                'earliest_date': earliest_date,
                'latest_date': latest_date,
                'indexes': len(stats.get('indexSizes', {}))
            }
            
        except Exception as ex:
            self._info.print(f"获取{code}集合统计信息异常: {ex}", DyLogData.error)
            return None
            
    def repairDatabase(self):
        """
        修复数据库问题
        """
        try:
            self._info.print("开始数据库修复...", DyLogData.ind)
            
            # 1. 检查健康状态
            health = self.checkDatabaseHealth()
            
            if not health['connection']:
                self._info.print("无法连接到数据库，请检查MongoDB服务", DyLogData.error)
                return False
            
            # 2. 修复索引问题
            if not health['indexes']:
                self._info.print("修复索引问题...", DyLogData.ind)
                self.optimizeDatabase()
            
            # 3. 清理临时文件和无效数据
            self._cleanupDatabase()
            
            self._info.print("数据库修复完成", DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f"数据库修复异常: {ex}", DyLogData.error)
            return False
    
    def _cleanupDatabase(self):
        """
        清理数据库中的无效数据
        """
        try:
            db = self._client[self.stock1minDbXTquant]
            collections = db.list_collection_names()
            
            cleaned_count = 0
            
            for collection_name in collections:
                try:
                    collection = db[collection_name]
                    
                    # 删除没有datetime字段的文档
                    result = collection.delete_many({'datetime': {'$exists': False}})
                    if result.deleted_count > 0:
                        self._info.print(f"从{collection_name}删除{result.deleted_count}条无效数据", DyLogData.ind)
                        cleaned_count += result.deleted_count
                    
                    # 删除datetime为None的文档
                    result = collection.delete_many({'datetime': None})
                    if result.deleted_count > 0:
                        self._info.print(f"从{collection_name}删除{result.deleted_count}条空时间数据", DyLogData.ind)
                        cleaned_count += result.deleted_count
                        
                except Exception as ex:
                    self._info.print(f"清理集合{collection_name}时出错: {ex}", DyLogData.warning)
            
            if cleaned_count > 0:
                self._info.print(f"数据清理完成，共清理{cleaned_count}条无效数据", DyLogData.ind)
            else:
                self._info.print("未发现需要清理的无效数据", DyLogData.ind)
                
        except Exception as ex:
            self._info.print(f"数据清理异常: {ex}", DyLogData.error)

    def _initializeSectorCollections(self):
        """初始化板块数据集合（新方法）"""
        try:
            # 调用旧方法确保兼容性
            self._createSectorCollections()
            
            # 为新的板块数据方法创建必要的索引
            self._createSectorIndexes()
            
            self._info.print("板块数据集合初始化完成", DyLogData.info)
            
        except Exception as ex:
            self._info.print(f"初始化板块数据集合异常: {ex}", DyLogData.warning)
    
    def _createSectorIndexes(self):
        """为板块数据创建索引"""
        try:
            # 为板块基本信息数据库创建索引
            sector_db = self._client[self.stockSectorDbXTquant]
            sector_types = ['industry', 'concept', 'region', 'other']
            
            for sector_type in sector_types:
                collection_name = f"sector_{sector_type}"
                collection = sector_db[collection_name]
                
                # 创建复合索引
                collection.create_index([('date', -1)])
                collection.create_index([('sector_code', 1)])
                collection.create_index([('date', -1), ('sector_code', 1)])
            
            # 为板块成分股数据库创建索引
            stocks_db = self._client[self.stockSectorStocksDbXTquant]
            stocks_collection = stocks_db['sector_stocks']
            
            stocks_collection.create_index([('date', -1)])
            stocks_collection.create_index([('sector_code', 1)])
            stocks_collection.create_index([('date', -1), ('sector_code', 1)])
            
        except Exception as ex:
            self._info.print(f"创建板块数据索引异常: {ex}", DyLogData.warning)
