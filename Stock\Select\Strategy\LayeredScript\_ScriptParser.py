import re
import pandas as pd
import numpy as np
import time
import signal
from contextlib import contextmanager
from ....EasyScriptingLanguageAPI.indicators import *
from DyCommon.DyCommon import DyLogData

# 自定义异常类
class ScriptError(Exception):
    """脚本执行基础异常"""
    pass

class ScriptTimeoutError(ScriptError):
    """脚本执行超时异常"""
    pass

class ScriptSyntaxError(ScriptError):
    """脚本语法错误异常"""
    pass

class ScriptExecutionError(ScriptError):
    """脚本执行错误异常"""
    pass

class ScriptDataError(ScriptError):
    """脚本数据错误异常"""
    pass

@contextmanager
def timeout(seconds):
    """超时上下文管理器（跨平台版本）"""
    import threading
    import platform

    if platform.system() == 'Windows':
        # Windows下使用线程计时器
        timer = None
        timeout_occurred = [False]

        def timeout_handler():
            timeout_occurred[0] = True

        timer = threading.Timer(seconds, timeout_handler)
        timer.start()

        try:
            yield
            if timeout_occurred[0]:
                raise ScriptTimeoutError(f"脚本执行超时 ({seconds}秒)")
        finally:
            if timer:
                timer.cancel()
    else:
        # Unix/Linux下使用信号
        def timeout_handler(signum, frame):
            raise ScriptTimeoutError(f"脚本执行超时 ({seconds}秒)")

        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(seconds)

        try:
            yield
        finally:
            signal.alarm(0)
            signal.signal(signal.SIGALRM, old_handler)

class DyStockScriptParser:
    """
    通达信风格脚本解析器（增强版）

    用于解析和执行通达信风格的指标脚本，支持选股条件判断
    增强功能：异常处理、超时保护、语法检查、调试支持
    """

    def __init__(self, script=None, debug=False, timeout_seconds=30):
        """
        初始化脚本解析器

        参数:
            script (str): 通达信风格的指标脚本
            debug (bool): 是否启用调试模式
            timeout_seconds (int): 脚本执行超时时间（秒）
        """
        self.script = script or ""
        self.variables = {}  # 存储变量值
        self.lines = []      # 存储处理后的脚本行
        self.debug = debug   # 调试模式
        self.timeout_seconds = timeout_seconds  # 超时时间
        self.execution_stats = {  # 执行统计
            'total_lines': 0,
            'successful_lines': 0,
            'failed_lines': 0,
            'execution_time': 0,
            'errors': []
        }

        # 初始化内建函数和变量
        self._init_builtins()

        if script:
            try:
                self._preprocess()
                self._validate_syntax()
            except Exception as e:
                raise ScriptSyntaxError(f"脚本预处理失败: {str(e)}")
    
    def _init_builtins(self):
        """初始化内建函数和变量"""
        # 定义常用指标函数映射（使用自定义实现）
        self.builtin_funcs = {
            'MA': self._ma,
            'EMA': self._ema,
            'SMA': self._sma,
            'CROSS': self._cross,
            'HHV': self._hhv,
            'LLV': self._llv,
            'REF': self._ref,
            'ABS': self._abs,
            'MAX': self._max,
            'MIN': self._min,
            'SUM': self._sum,
            'EVERY': self._every,
            'EXIST': self._exist,
            'FINANCE': self._finance,
            'NAMEINCLUDE': self._nameinclude,
            'DYNAINFO': self._dynainfo,
            'ZTPRICE': self._ztprice,
            'DTPRICE': self._dtprice,
            'IF': self._if,
            'IFN': self._ifn,
            'BARSLAST': self._barslast,
            'COUNT': self._count,
        }
    
    def _preprocess(self):
        """预处理脚本"""
        # 去除注释和空行
        lines = []
        line_number = 0
        for line in self.script.split('\n'):
            line_number += 1
            # 去除行尾注释
            original_line = line
            line = re.sub(r'#.*$', '', line).strip()
            if line:
                # 添加行号信息用于调试
                lines.append({'content': line, 'line_number': line_number, 'original': original_line})

        self.lines = lines
        self.execution_stats['total_lines'] = len(lines)

    def _validate_syntax(self):
        """验证脚本语法"""
        errors = []

        for line_info in self.lines:
            line = line_info['content']
            line_number = line_info['line_number']

            # 检查赋值语句格式
            if ':=' not in line:
                errors.append(f"第{line_number}行: 缺少赋值操作符':=' - {line}")
                continue

            # 检查变量名格式
            match = re.match(r'(\w+)\s*:=\s*(.+?)\s*;?\s*$', line)
            if not match:
                errors.append(f"第{line_number}行: 语法格式错误 - {line}")
                continue

            var_name = match.group(1)
            expression = match.group(2)

            # 检查变量名是否合法
            if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', var_name):
                errors.append(f"第{line_number}行: 变量名格式错误 '{var_name}' - {line}")

            # 检查表达式是否为空
            if not expression.strip():
                errors.append(f"第{line_number}行: 表达式为空 - {line}")

        if errors:
            error_msg = "脚本语法验证失败:\n" + "\n".join(errors)
            raise ScriptSyntaxError(error_msg)

        if self.debug:
            print(f"脚本语法验证通过，共{len(self.lines)}行有效代码")
    
    def set_script(self, script):
        """设置脚本内容"""
        self.script = script
        self._preprocess()
    
    def _execute_line(self, line_info, data):
        """
        执行单行脚本（安全版本）

        参数:
            line_info (dict): 脚本行信息，包含content, line_number, original
            data (dict): 数据字典，包含C, O, H, L, V等基础数据

        返回:
            (str, object): 变量名和值
        """
        line = line_info['content']
        line_number = line_info['line_number']

        try:
            return self._execute_line_safe(line, line_number, data)
        except ScriptError:
            # 重新抛出脚本相关异常
            raise
        except Exception as e:
            # 包装其他异常
            error_msg = f"第{line_number}行执行失败: {str(e)}\n原始代码: {line_info['original']}"
            raise ScriptExecutionError(error_msg)

    def _execute_line_safe(self, line, line_number, data):
        """
        安全执行单行脚本

        参数:
            line (str): 脚本行内容
            line_number (int): 行号
            data (dict): 数据字典

        返回:
            (str, object): 变量名和值
        """
        # 验证数据完整性
        self._validate_data(data, line_number)

        # 解析赋值语句
        match = re.match(r'(\w+)\s*:=\s*(.+?)\s*;?\s*$', line)
        if not match:
            raise ScriptSyntaxError(f"第{line_number}行: 无法解析赋值语句 - {line}")

        var_name = match.group(1)
        expression = match.group(2)

        # 替换变量引用
        try:
            expression = self._replace_variables(expression)
            expression = self._replace_data_references(expression, data)
            expression = self._replace_function_calls(expression)
            expression = self._replace_logical_operators(expression)
        except Exception as e:
            raise ScriptExecutionError(f"第{line_number}行: 表达式处理失败 - {str(e)}")

        # 执行表达式
        try:
            if self.debug:
                print(f"第{line_number}行执行: {expression}")

            # 创建安全的执行环境
            safe_globals = self._create_safe_globals()
            safe_locals = {"self": self, "data": data, "np": np, "pd": pd}

            # 预处理表达式，确保类型兼容
            processed_expression = self._preprocess_expression(expression)

            # 执行表达式
            result = eval(processed_expression, safe_globals, safe_locals)

            # 验证结果
            result = self._validate_result(result, var_name, line_number)

            # 存储结果
            self.variables[var_name] = result

            if self.debug:
                print(f"  变量 {var_name} = {type(result).__name__}")
                if isinstance(result, (np.ndarray, pd.Series)) and len(result) > 0:
                    print(f"  最后值: {result[-1]}")

            return var_name, result

        except Exception as e:
            error_msg = f"第{line_number}行: 表达式执行失败\n"
            error_msg += f"  原始表达式: {line}\n"
            error_msg += f"  处理后表达式: {expression}\n"
            error_msg += f"  错误信息: {str(e)}"
            raise ScriptExecutionError(error_msg)
    
    def _validate_data(self, data, line_number):
        """验证输入数据的完整性"""
        if not isinstance(data, dict):
            raise ScriptDataError(f"第{line_number}行: 数据格式错误，期望字典类型")

        required_keys = ['C', 'O', 'H', 'L', 'V']
        missing_keys = [key for key in required_keys if key not in data]
        if missing_keys:
            raise ScriptDataError(f"第{line_number}行: 缺少必要的数据字段: {missing_keys}")

        # 检查数据长度一致性
        data_lengths = {key: len(value) if hasattr(value, '__len__') else 1
                       for key, value in data.items() if key in required_keys}

        if len(set(data_lengths.values())) > 1:
            raise ScriptDataError(f"第{line_number}行: 数据长度不一致: {data_lengths}")

    def _replace_variables(self, expression):
        """替换变量引用"""
        for name, value in sorted(self.variables.items(), key=lambda x: len(x[0]), reverse=True):
            # 所有变量都通过self.variables访问，避免直接替换为值
            expression = re.sub(rf'\b{name}\b', f'self.variables["{name}"]', expression)
        return expression

    def _replace_data_references(self, expression, data):
        """替换基础数据引用"""
        for key in sorted(data.keys(), key=len, reverse=True):
            expression = re.sub(rf'\b{key}\b', f'data["{key}"]', expression)
        return expression

    def _replace_function_calls(self, expression):
        """替换函数调用"""
        for func_name in self.builtin_funcs:
            expression = re.sub(rf'\b{func_name}\b', f'self._{func_name.lower()}', expression)
        return expression

    def _replace_logical_operators(self, expression):
        """替换逻辑运算符"""
        # 暂时保持简单的替换，在执行时处理类型问题
        expression = expression.replace(' AND ', ' & ')
        expression = expression.replace(' OR ', ' | ')
        expression = expression.replace(' NOT ', ' ~ ')
        return expression

    def _create_safe_globals(self):
        """创建安全的全局命名空间"""
        def safe_and(a, b):
            """安全的AND运算，处理不同类型的数组"""
            # 确保两个操作数都是布尔类型的numpy数组
            if isinstance(a, np.ndarray):
                a = a.astype(bool)
            if isinstance(b, np.ndarray):
                b = b.astype(bool)
            return a & b

        def safe_or(a, b):
            """安全的OR运算，处理不同类型的数组"""
            # 确保两个操作数都是布尔类型的numpy数组
            if isinstance(a, np.ndarray):
                a = a.astype(bool)
            if isinstance(b, np.ndarray):
                b = b.astype(bool)
            return a | b

        safe_globals = {
            '__builtins__': {
                'len': len, 'max': max, 'min': min, 'abs': abs,
                'round': round, 'int': int, 'float': float, 'bool': bool,
                'str': str, 'list': list, 'dict': dict, 'tuple': tuple,
                'range': range, 'enumerate': enumerate, 'zip': zip,
                'isinstance': isinstance, 'hasattr': hasattr,
                'True': True, 'False': False, 'None': None
            },
            'np': np,
            'pd': pd,
            'safe_and': safe_and,
            'safe_or': safe_or
        }
        return safe_globals

    def _validate_result(self, result, var_name, line_number):
        """验证计算结果"""
        if result is None:
            raise ScriptExecutionError(f"第{line_number}行: 变量 {var_name} 计算结果为None")

        # 检查数组结果
        if isinstance(result, (np.ndarray, pd.Series)):
            if len(result) == 0:
                raise ScriptExecutionError(f"第{line_number}行: 变量 {var_name} 计算结果为空数组")

            # 检查是否包含无效值
            if isinstance(result, np.ndarray) and np.isnan(result).all():
                raise ScriptExecutionError(f"第{line_number}行: 变量 {var_name} 计算结果全为NaN")

        return result

    def _preprocess_expression(self, expression):
        """预处理表达式，确保类型兼容"""
        # 对于包含位运算符的表达式，确保操作数类型一致
        if '&' in expression or '|' in expression:
            import re

            # 为所有比较表达式添加括号，确保正确的运算优先级
            # 匹配比较运算符两边的表达式
            comparison_pattern = r'([^&|()]+(?:[><=!]+[^&|()]+))'

            def add_parentheses(match):
                expr = match.group(1).strip()
                # 如果表达式包含比较运算符且没有括号，添加括号
                if any(op in expr for op in ['>', '<', '>=', '<=', '==', '!=']):
                    if not (expr.startswith('(') and expr.endswith(')')):
                        return f'({expr})'
                return expr

            # 为比较表达式添加括号
            expression = re.sub(comparison_pattern, add_parentheses, expression)

        return expression

    # 技术指标函数实现
    def _ma(self, data, n):
        """移动平均线"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=n, min_periods=1).mean().values

    def _ema(self, data, n):
        """指数移动平均线"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.ewm(span=n, adjust=False).mean().values

    def _sma(self, data, n, m=1):
        """简单移动平均"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=n, min_periods=1).mean().values

    def _cross(self, a, b):
        """上穿判断"""
        if isinstance(a, np.ndarray):
            a = pd.Series(a)
        if isinstance(b, np.ndarray):
            b = pd.Series(b)

        # 计算前一天的关系
        prev_relation = (a.shift(1) <= b.shift(1))
        # 计算当前的关系
        curr_relation = (a > b)
        # 上穿条件：前一天小于等于，当前大于
        cross_up = prev_relation & curr_relation

        # 返回整个数组，填充NaN为False，保持布尔类型
        result = cross_up.fillna(False).values
        return result

    def _ref(self, data, n):
        """引用n天前的数据"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)

        # 对于布尔类型数据，需要特殊处理NaN值
        shifted = data.shift(n)
        if data.dtype == bool or (hasattr(data, 'dtype') and data.dtype == 'bool'):
            # 布尔数组的NaN填充为False
            return shifted.fillna(False).astype(bool).values
        else:
            return shifted.values

    def _hhv(self, data, n):
        """n天内最高值"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=n, min_periods=1).max().values

    def _llv(self, data, n):
        """n天内最低值"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=n, min_periods=1).min().values

    def _sum(self, data, n):
        """n天累计和"""
        if isinstance(data, np.ndarray):
            data = pd.Series(data)
        return data.rolling(window=n, min_periods=1).sum().values

    def _abs(self, data):
        """绝对值"""
        if isinstance(data, (np.ndarray, pd.Series)):
            return np.abs(data)
        return abs(data)

    def _max(self, a, b):
        """最大值"""
        return np.maximum(a, b)

    def _min(self, a, b):
        """最小值"""
        return np.minimum(a, b)

    # 自定义函数实现
    def _every(self, condition, n):
        """所有都满足条件"""
        if isinstance(condition, (np.ndarray, pd.Series)):
            if len(condition) >= n:
                return np.all(condition[-n:])
        return False
    
    def _exist(self, condition, n):
        """存在满足条件"""
        if isinstance(condition, (np.ndarray, pd.Series)):
            if len(condition) >= n:
                return np.any(condition[-n:])
        return False
    
    def _finance(self, code):
        """获取财务数据"""
        # 这里简化处理，实际应该根据code获取相应的财务数据
        if code == 7:  # 假设7代表流通股本（万股）
            return 10000  # 模拟值
        elif code == 40:  # 假设40代表总市值
            return 50000000000  # 模拟值
        elif code == 3:  # 假设3代表所属行业
            return 2  # 模拟值
        return 0

    def _barslast(self, condition):
        """
        计算上一次满足条件到当前的周期数

        参数:
            condition: 条件数组（布尔值）

        返回:
            int: 距离上次满足条件的周期数，如果从未满足则返回数据长度
        """
        if isinstance(condition, (np.ndarray, pd.Series)):
            condition = np.array(condition, dtype=bool)
            # 找到最后一个True的位置
            true_indices = np.where(condition)[0]
            if len(true_indices) > 0:
                last_true_index = true_indices[-1]
                return len(condition) - 1 - last_true_index
            else:
                return len(condition)
        return 0

    def _count(self, condition, n):
        """
        计算n个周期内满足条件的次数

        参数:
            condition: 条件数组（布尔值）
            n: 周期数

        返回:
            int: 满足条件的次数
        """
        if isinstance(condition, (np.ndarray, pd.Series)):
            condition = np.array(condition, dtype=bool)
            if len(condition) >= n:
                return np.sum(condition[-n:])
            else:
                return np.sum(condition)
        return 0

    def _nameinclude(self, keyword):
        """股票名称包含关键字"""
        # 需要外部设置股票名称
        stock_name = self.variables.get('STOCKNAME', '')
        return keyword in stock_name
    
    def _dynainfo(self, code):
        """获取动态行情数据"""
        # 这里简化处理，实际应该根据code获取相应的动态数据
        if code == 37:  # 假设37是涨幅
            return 0.2  # 模拟值
        return 0
    
    def _ztprice(self, price, ratio):
        """计算涨停价"""
        return price * (1 + ratio)
    
    def _dtprice(self, price, ratio):
        """计算跌停价"""
        return price * (1 - ratio)
    
    def _if(self, condition, true_value, false_value):
        """条件选择"""
        if isinstance(condition, (np.ndarray, pd.Series)):
            return np.where(condition, true_value, false_value)
        return true_value if condition else false_value
    
    def _ifn(self, value, default_value):
        """空值处理"""
        if isinstance(value, (np.ndarray, pd.Series)):
            return np.where(np.isnan(value), default_value, value)
        return default_value if pd.isna(value) else value
    
    def execute(self, data, stock_name=None):
        """
        执行脚本（增强版）

        参数:
            data (dict): 数据字典，包含C, O, H, L, V等基础数据
            stock_name (str): 股票名称，用于NAMEINCLUDE函数
        """
        start_time = time.time()

        # 重置执行统计
        self.execution_stats.update({
            'successful_lines': 0,
            'failed_lines': 0,
            'execution_time': 0,
            'errors': []
        })

        try:
            # 使用超时保护执行脚本
            with timeout(self.timeout_seconds):
                self._execute_script_safe(data, stock_name)
        except ScriptTimeoutError as e:
            self.execution_stats['errors'].append(str(e))
            raise
        except Exception as e:
            self.execution_stats['errors'].append(str(e))
            raise
        finally:
            self.execution_stats['execution_time'] = time.time() - start_time

            if self.debug:
                self._print_execution_stats()

    def _execute_script_safe(self, data, stock_name):
        """安全执行脚本主体"""
        # 清空之前的变量
        self.variables.clear()

        # 设置股票名称
        if stock_name:
            self.variables['STOCKNAME'] = stock_name

        # 逐行执行脚本
        for line_info in self.lines:
            try:
                self._execute_line(line_info, data)
                self.execution_stats['successful_lines'] += 1
            except ScriptError as e:
                self.execution_stats['failed_lines'] += 1
                self.execution_stats['errors'].append(str(e))

                if self.debug:
                    print(f"脚本执行错误: {e}")

                # 根据错误类型决定是否继续执行
                if isinstance(e, (ScriptSyntaxError, ScriptDataError)):
                    # 语法错误和数据错误直接终止
                    raise
                else:
                    # 其他错误可以继续执行
                    continue

    def _print_execution_stats(self):
        """打印执行统计信息"""
        stats = self.execution_stats
        print(f"\n=== 脚本执行统计 ===")
        print(f"总行数: {stats['total_lines']}")
        print(f"成功行数: {stats['successful_lines']}")
        print(f"失败行数: {stats['failed_lines']}")
        print(f"执行时间: {stats['execution_time']:.3f}秒")
        print(f"成功率: {stats['successful_lines']/stats['total_lines']*100:.1f}%")

        if stats['errors']:
            print(f"错误列表:")
            for i, error in enumerate(stats['errors'][:5], 1):  # 只显示前5个错误
                print(f"  {i}. {error}")
            if len(stats['errors']) > 5:
                print(f"  ... 还有{len(stats['errors'])-5}个错误")
        print("===================")
    
    def get_result(self, result_var='SELECT_STOCK'):
        """
        获取最终结果
        
        参数:
            result_var (str): 结果变量名
            
        返回:
            bool: 最终判断结果
        """
        if result_var in self.variables:
            result = self.variables[result_var]
            if isinstance(result, (np.ndarray, pd.Series)):
                return bool(result[-1]) if len(result) > 0 else False
            else:
                return bool(result)
        return False
    
    def get_intermediate_result(self, var_name):
        """
        获取中间变量结果

        参数:
            var_name (str): 变量名

        返回:
            任意类型: 变量值
        """
        if var_name in self.variables:
            result = self.variables[var_name]
            if isinstance(result, (np.ndarray, pd.Series)):
                return result[-1] if len(result) > 0 else 0
            else:
                return result
        return 0

    def get_all_variables(self):
        """获取所有变量及其最新值"""
        result = {}
        for var_name, value in self.variables.items():
            if isinstance(value, (np.ndarray, pd.Series)):
                result[var_name] = value[-1] if len(value) > 0 else None
            else:
                result[var_name] = value
        return result

    def get_execution_stats(self):
        """获取执行统计信息"""
        return self.execution_stats.copy()

    def validate_script(self, data_sample=None):
        """
        验证脚本有效性（不执行）

        参数:
            data_sample (dict): 样本数据用于验证

        返回:
            dict: 验证结果
        """
        validation_result = {
            'syntax_valid': True,
            'data_compatible': True,
            'errors': [],
            'warnings': []
        }

        try:
            # 语法验证
            self._validate_syntax()
        except ScriptSyntaxError as e:
            validation_result['syntax_valid'] = False
            validation_result['errors'].append(f"语法错误: {str(e)}")

        # 数据兼容性验证
        if data_sample:
            try:
                for line_info in self.lines:
                    line_number = line_info['line_number']
                    self._validate_data(data_sample, line_number)
            except ScriptDataError as e:
                validation_result['data_compatible'] = False
                validation_result['errors'].append(f"数据兼容性错误: {str(e)}")

        # 检查常见问题
        self._check_common_issues(validation_result)

        return validation_result

    def _check_common_issues(self, validation_result):
        """检查常见问题"""
        # 检查是否有SELECT_STOCK变量
        has_select_stock = any('SELECT_STOCK' in line_info['content']
                              for line_info in self.lines)
        if not has_select_stock:
            validation_result['warnings'].append("未找到SELECT_STOCK变量，可能无法正确返回选股结果")

        # 检查是否使用了未定义的函数
        used_functions = set()
        for line_info in self.lines:
            content = line_info['content']
            # 简单的函数调用检测
            functions = re.findall(r'\b([A-Z_]+)\s*\(', content)
            used_functions.update(functions)

        undefined_functions = used_functions - set(self.builtin_funcs.keys())
        if undefined_functions:
            validation_result['warnings'].append(f"使用了未定义的函数: {list(undefined_functions)}")

    def reset(self):
        """重置解析器状态"""
        self.variables.clear()
        self.execution_stats.update({
            'successful_lines': 0,
            'failed_lines': 0,
            'execution_time': 0,
            'errors': []
        })

# 为了兼容策略加载器的命名约定，添加别名  
ScriptParser = DyStockScriptParser 

# 为了兼容文件名命名约定，添加下划线开头的别名
_ScriptParser = DyStockScriptParser 