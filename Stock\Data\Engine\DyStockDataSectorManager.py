"""
板块数据管理器
基于xtquant知识库完善板块数据功能，支持选股、回测、交易模式下的板块数据读取
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from datetime import datetime, timedelta
import pandas as pd
import json


class DyStockDataSectorManager(object):
    """板块数据管理器"""
    
    def __init__(self, mongoDbEngine, gateway, info):
        """
        @param mongoDbEngine: MongoDB引擎
        @param gateway: 数据网关
        @param info: 信息输出对象
        """
        self._mongoDbEngine = mongoDbEngine
        self._gateway = gateway
        self._info = info
        
        # 板块数据缓存
        self._sectorCache = {
            'sectors': {},          # 板块列表缓存
            'constituents': {},     # 成分股缓存
            'lastUpdate': None      # 最后更新时间
        }
        
        # 缓存有效期（秒）
        self._cacheExpiry = 3600  # 1小时
        
        # 板块分类映射
        self._sectorCategories = {
            'TGN': '同花顺概念板块',
            'THY': '同花顺行业板块',
            'SW': '申万行业板块',
            'GN': '概念板块',
            'DY': '地域板块',
            'CSRC': '证监会行业板块',
            'HKSW': '港股申万行业板块'
        }
    
    def getSectorList(self, keywords=None, category=None):
        """
        获取板块列表
        @param keywords: 关键字过滤列表
        @param category: 板块分类过滤
        @return: list 板块列表
        """
        try:
            # 检查缓存
            if self._isCacheValid('sectors'):
                sectors = self._sectorCache['sectors'].get('all', [])
            else:
                # 从xtquant获取板块列表
                if hasattr(self._gateway, 'getSectorList'):
                    sectors = self._gateway.getSectorList(keywords)
                else:
                    # 使用MCP服务获取
                    sectors = self._getSectorListFromMCP(keywords)
                
                # 更新缓存
                self._sectorCache['sectors']['all'] = sectors
                self._sectorCache['lastUpdate'] = datetime.now()
            
            # 应用过滤条件
            filtered_sectors = self._filterSectors(sectors, keywords, category)
            
            return filtered_sectors
            
        except Exception as ex:
            self._info.print(f'获取板块列表异常: {ex}', DyLogData.error)
            return []
    
    def getSectorConstituents(self, sector):
        """
        获取板块成分股
        @param sector: 板块名称
        @return: list [[code, name], ...] 成分股列表
        """
        try:
            # 检查缓存
            cache_key = f'constituents_{sector}'
            if self._isCacheValid(cache_key):
                return self._sectorCache['constituents'][cache_key]
            
            # 从xtquant获取成分股
            if hasattr(self._gateway, 'getSectorConstituents'):
                constituents = self._gateway.getSectorConstituents(sector)
            else:
                # 使用MCP服务获取
                constituents = self._getSectorConstituentsFromMCP(sector)
            
            # 更新缓存
            self._sectorCache['constituents'][cache_key] = constituents
            
            return constituents
            
        except Exception as ex:
            self._info.print(f'获取板块成分股异常: {ex}', DyLogData.error)
            return []
    
    def getSectorsByStock(self, stockCode):
        """
        获取股票所属的所有板块
        @param stockCode: 股票代码
        @return: list 板块列表
        """
        try:
            # 从数据库查询
            sectors = self._mongoDbEngine.getStockSectors(stockCode)
            
            if not sectors:
                # 如果数据库没有，尝试从网关获取
                if hasattr(self._gateway, 'getStockSectors'):
                    sectors = self._gateway.getStockSectors(stockCode)
                    
                    # 保存到数据库
                    if sectors:
                        self._mongoDbEngine.saveStockSectors(stockCode, sectors)
            
            return sectors or []
            
        except Exception as ex:
            self._info.print(f'获取股票板块异常: {ex}', DyLogData.error)
            return []
    
    def updateSectorData(self, forceUpdate=False):
        """
        更新板块数据
        @param forceUpdate: 是否强制更新
        @return: bool
        """
        try:
            self._info.print('开始更新板块数据...', DyLogData.ind)
            
            # 获取所有板块列表
            sectors = self.getSectorList()
            
            if not sectors:
                self._info.print('获取板块列表失败', DyLogData.error)
                return False
            
            # 更新板块基本信息
            self._updateSectorInfo(sectors)
            
            # 更新成分股数据（分批处理）
            batch_size = 50
            total_sectors = len(sectors)
            
            for i in range(0, total_sectors, batch_size):
                batch_sectors = sectors[i:i+batch_size]
                self._updateSectorConstituents(batch_sectors)
                
                # 显示进度
                progress = min(i + batch_size, total_sectors)
                self._info.print(f'更新板块成分股进度: {progress}/{total_sectors}', DyLogData.ind)
            
            # 清除缓存
            self._clearCache()
            
            self._info.print('板块数据更新完成', DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f'更新板块数据异常: {ex}', DyLogData.error)
            return False
    
    def getSectorPerformance(self, sectors, startDate, endDate):
        """
        获取板块表现数据
        @param sectors: 板块列表
        @param startDate: 开始日期
        @param endDate: 结束日期
        @return: DataFrame 板块表现数据
        """
        try:
            performance_data = []
            
            for sector in sectors:
                # 获取板块成分股
                constituents = self.getSectorConstituents(sector)
                
                if not constituents:
                    continue
                
                # 计算板块表现
                sector_perf = self._calculateSectorPerformance(
                    sector, constituents, startDate, endDate
                )
                
                if sector_perf:
                    performance_data.append(sector_perf)
            
            # 转换为DataFrame
            if performance_data:
                df = pd.DataFrame(performance_data)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as ex:
            self._info.print(f'获取板块表现异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def _getSectorListFromMCP(self, keywords=None):
        """通过MCP服务获取板块列表"""
        try:
            # 这里应该调用MCP服务
            # 暂时返回空列表，实际实现时需要集成MCP调用
            return []
        except Exception:
            return []
    
    def _getSectorConstituentsFromMCP(self, sector):
        """通过MCP服务获取板块成分股"""
        try:
            # 这里应该调用MCP服务
            # 暂时返回空列表，实际实现时需要集成MCP调用
            return []
        except Exception:
            return []
    
    def _filterSectors(self, sectors, keywords=None, category=None):
        """过滤板块列表"""
        filtered = sectors
        
        # 按关键字过滤
        if keywords:
            filtered = [s for s in filtered if any(kw in s for kw in keywords)]
        
        # 按分类过滤
        if category:
            filtered = [s for s in filtered if s.startswith(category)]
        
        return filtered
    
    def _isCacheValid(self, cache_key):
        """检查缓存是否有效"""
        if cache_key == 'sectors':
            return (self._sectorCache['lastUpdate'] and 
                   (datetime.now() - self._sectorCache['lastUpdate']).total_seconds() < self._cacheExpiry and
                   'all' in self._sectorCache['sectors'])
        else:
            return cache_key in self._sectorCache['constituents']
    
    def _updateSectorInfo(self, sectors):
        """更新板块基本信息到数据库"""
        try:
            sector_info = []
            
            for sector in sectors:
                # 解析板块分类
                category = self._parseSectorCategory(sector)
                
                info = {
                    'code': sector,
                    'name': sector,
                    'category': category,
                    'updateTime': datetime.now()
                }
                sector_info.append(info)
            
            # 保存到数据库
            self._mongoDbEngine.saveSectorInfo(sector_info)
            
        except Exception as ex:
            self._info.print(f'更新板块信息异常: {ex}', DyLogData.error)
    
    def _updateSectorConstituents(self, sectors):
        """更新板块成分股到数据库"""
        try:
            for sector in sectors:
                constituents = self.getSectorConstituents(sector)
                
                if constituents:
                    # 保存到数据库
                    self._mongoDbEngine.saveSectorConstituents(sector, constituents)
                    
        except Exception as ex:
            self._info.print(f'更新板块成分股异常: {ex}', DyLogData.error)
    
    def _parseSectorCategory(self, sector):
        """解析板块分类"""
        for prefix, category in self._sectorCategories.items():
            if sector.startswith(prefix):
                return category
        return '其他板块'
    
    def _calculateSectorPerformance(self, sector, constituents, startDate, endDate):
        """计算板块表现"""
        try:
            # 获取成分股价格数据
            stock_codes = [item[0] for item in constituents]
            
            # 计算板块指数（等权重）
            total_return = 0
            valid_stocks = 0
            
            for code in stock_codes:
                # 获取股票价格数据
                df = self._mongoDbEngine.getOneCodeDays(
                    code, startDate, endDate, ['close'], None
                )
                
                if df is not None and len(df) >= 2:
                    start_price = df.iloc[0]['close']
                    end_price = df.iloc[-1]['close']
                    
                    if start_price > 0:
                        stock_return = (end_price - start_price) / start_price
                        total_return += stock_return
                        valid_stocks += 1
            
            if valid_stocks > 0:
                avg_return = total_return / valid_stocks
                
                return {
                    'sector': sector,
                    'return': avg_return,
                    'stock_count': len(constituents),
                    'valid_stocks': valid_stocks,
                    'start_date': startDate,
                    'end_date': endDate
                }
            
            return None
            
        except Exception as ex:
            self._info.print(f'计算板块表现异常: {ex}', DyLogData.error)
            return None
    
    def _clearCache(self):
        """清除缓存"""
        self._sectorCache = {
            'sectors': {},
            'constituents': {},
            'lastUpdate': None
        }
    
    def getSectorCategories(self):
        """获取板块分类"""
        return self._sectorCategories
    
    def searchSectors(self, keyword):
        """搜索板块"""
        try:
            all_sectors = self.getSectorList()
            
            # 模糊搜索
            matched_sectors = [s for s in all_sectors if keyword.lower() in s.lower()]
            
            return matched_sectors
            
        except Exception as ex:
            self._info.print(f'搜索板块异常: {ex}', DyLogData.error)
            return []
