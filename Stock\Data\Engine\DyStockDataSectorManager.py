"""
板块数据管理器
基于xtquant知识库完善板块数据功能，支持选股、回测、交易模式下的板块数据读取
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from datetime import datetime, timedelta
import pandas as pd
import json


class DyStockDataSectorManager(object):
    """板块数据管理器"""
    
    def __init__(self, mongoDbEngine, gateway, info):
        """
        @param mongoDbEngine: MongoDB引擎
        @param gateway: 数据网关
        @param info: 信息输出对象
        """
        self._mongoDbEngine = mongoDbEngine
        self._gateway = gateway
        self._info = info

        # 板块数据缓存
        self._sectorCache = {
            'sectors': {},          # 板块列表缓存
            'constituents': {},     # 成分股缓存
            'lastUpdate': None      # 最后更新时间
        }

        # 缓存有效期（秒）
        self._cacheExpiry = 3600  # 1小时

        # 板块分类映射
        self._sectorCategories = {
            'TGN': '同花顺概念板块',
            'THY': '同花顺行业板块',
            'SW': '申万行业板块',
            'GN': '概念板块',
            'DY': '地域板块',
            'CSRC': '证监会行业板块',
            'HKSW': '港股申万行业板块'
        }

        # 初始化缓存
        self._initCache()
    
    def getSectorList(self, keywords=None, category=None):
        """
        获取板块列表
        @param keywords: 关键字过滤列表
        @param category: 板块分类过滤
        @return: list 板块列表
        """
        try:
            # 检查缓存
            if self._isCacheValid('sectors'):
                self._info.print('使用缓存的板块列表数据', DyLogData.ind)
                sectors = self._sectorCache['sectors'].get('all', [])
            else:
                self._info.print('获取板块列表数据...', DyLogData.ind)

                # 通过数据网关获取板块列表
                self._info.print('通过数据网关获取板块列表', DyLogData.ind)
                sectors = self._getSectorListFromGateway(keywords)

                # 检查结果
                if not sectors:
                    self._info.print('获取板块列表失败，使用模拟数据', DyLogData.warning)
                    sectors = self._getMockSectorList()

                # 更新缓存
                self._sectorCache['sectors']['all'] = sectors
                self._sectorCache['lastUpdate'] = datetime.now()

                self._info.print(f'获取到{len(sectors)}个板块', DyLogData.ind)

            # 应用过滤条件
            filtered_sectors = self._filterSectors(sectors, keywords, category)

            if keywords or category:
                self._info.print(f'过滤后剩余{len(filtered_sectors)}个板块', DyLogData.ind)

            return filtered_sectors

        except Exception as ex:
            self._info.print(f'获取板块列表异常: {ex}', DyLogData.error)
            # 返回模拟数据作为备选
            return self._getMockSectorList()
    
    def getSectorConstituents(self, sector):
        """
        获取板块成分股
        @param sector: 板块名称
        @return: list [[code, name], ...] 成分股列表
        """
        try:
            if not sector:
                self._info.print('板块名称不能为空', DyLogData.warning)
                return []

            # 检查缓存
            cache_key = f'constituents_{sector}'
            if self._isCacheValid(cache_key):
                self._info.print(f'使用缓存的板块[{sector}]成分股数据', DyLogData.ind)
                return self._sectorCache['constituents'][cache_key]

            self._info.print(f'获取板块[{sector}]成分股数据...', DyLogData.ind)

            # 通过数据网关获取成分股
            self._info.print('通过数据网关获取成分股', DyLogData.ind)
            constituents = self._getSectorConstituentsFromGateway(sector)

            # 检查结果
            if not constituents:
                self._info.print(f'获取板块[{sector}]成分股失败，使用模拟数据', DyLogData.warning)
                constituents = self._getMockConstituents(sector)

            # 更新缓存
            self._sectorCache['constituents'][cache_key] = constituents

            self._info.print(f'板块[{sector}]获取到{len(constituents)}只成分股', DyLogData.ind)

            return constituents

        except Exception as ex:
            self._info.print(f'获取板块成分股异常: {ex}', DyLogData.error)
            # 返回模拟数据作为备选
            return self._getMockConstituents(sector)
    
    def getSectorsByStock(self, stockCode):
        """
        获取股票所属的所有板块
        @param stockCode: 股票代码
        @return: list 板块列表
        """
        try:
            # 从数据库查询
            sectors = self._mongoDbEngine.getStockSectors(stockCode)
            
            if not sectors:
                # 如果数据库没有，尝试从网关获取
                if hasattr(self._gateway, 'getStockSectors'):
                    sectors = self._gateway.getStockSectors(stockCode)
                    
                    # 保存到数据库
                    if sectors:
                        self._mongoDbEngine.saveStockSectors(stockCode, sectors)
            
            return sectors or []
            
        except Exception as ex:
            self._info.print(f'获取股票板块异常: {ex}', DyLogData.error)
            return []
    
    def updateSectorData(self, forceUpdate=False):
        """
        更新板块数据
        @param forceUpdate: 是否强制更新
        @return: bool
        """
        try:
            self._info.print('开始更新板块数据...', DyLogData.ind)
            
            # 获取所有板块列表
            sectors = self.getSectorList()
            
            if not sectors:
                self._info.print('获取板块列表失败', DyLogData.error)
                return False
            
            # 更新板块基本信息
            self._updateSectorInfo(sectors)
            
            # 更新成分股数据（分批处理）
            batch_size = 50
            total_sectors = len(sectors)
            
            for i in range(0, total_sectors, batch_size):
                batch_sectors = sectors[i:i+batch_size]
                self._updateSectorConstituents(batch_sectors)
                
                # 显示进度
                progress = min(i + batch_size, total_sectors)
                self._info.print(f'更新板块成分股进度: {progress}/{total_sectors}', DyLogData.ind)
            
            # 清除缓存
            self._clearCache()
            
            self._info.print('板块数据更新完成', DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f'更新板块数据异常: {ex}', DyLogData.error)
            return False
    
    def getSectorPerformance(self, sectors, startDate, endDate):
        """
        获取板块表现数据
        @param sectors: 板块列表
        @param startDate: 开始日期
        @param endDate: 结束日期
        @return: DataFrame 板块表现数据
        """
        try:
            performance_data = []
            
            for sector in sectors:
                # 获取板块成分股
                constituents = self.getSectorConstituents(sector)
                
                if not constituents:
                    continue
                
                # 计算板块表现
                sector_perf = self._calculateSectorPerformance(
                    sector, constituents, startDate, endDate
                )
                
                if sector_perf:
                    performance_data.append(sector_perf)
            
            # 转换为DataFrame
            if performance_data:
                df = pd.DataFrame(performance_data)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as ex:
            self._info.print(f'获取板块表现异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def _getSectorListFromGateway(self, keywords=None):
        """通过数据网关获取板块列表"""
        try:
            # 通过数据网关获取板块列表
            if hasattr(self._gateway, 'getSectorList') and callable(getattr(self._gateway, 'getSectorList')):
                sectors = self._gateway.getSectorList(keywords)
                if sectors and isinstance(sectors, list):
                    return sectors

            # 如果网关不可用，返回模拟数据
            self._info.print('数据网关获取板块列表失败，使用模拟数据', DyLogData.warning)
            return self._getMockSectorList()

        except Exception as ex:
            self._info.print(f'数据网关获取板块列表异常: {ex}', DyLogData.warning)
            # 返回模拟数据
            return self._getMockSectorList()

    def _getSectorConstituentsFromGateway(self, sector):
        """通过数据网关获取板块成分股"""
        try:
            # 通过数据网关获取成分股
            if hasattr(self._gateway, 'getSectorConstituents') and callable(getattr(self._gateway, 'getSectorConstituents')):
                constituents = self._gateway.getSectorConstituents(sector)
                if constituents and isinstance(constituents, list):
                    return constituents

            # 如果网关不可用，返回模拟数据
            self._info.print(f'数据网关获取板块[{sector}]成分股失败，使用模拟数据', DyLogData.warning)
            return self._getMockConstituents(sector)

        except Exception as ex:
            self._info.print(f'数据网关获取板块成分股异常: {ex}', DyLogData.warning)
            # 返回模拟数据
            return self._getMockConstituents(sector)

    def _getMockSectorList(self):
        """获取模拟板块列表"""
        return [
            # 同花顺概念板块
            'TGN人工智能', 'TGN芯片', 'TGN新能源汽车', 'TGN5G概念', 'TGN大数据', 'TGN云计算',
            'TGN物联网', 'TGN区块链', 'TGN虚拟现实', 'TGN工业互联网', 'TGN数字货币', 'TGN网络安全',

            # 同花顺行业板块
            'THY计算机应用', 'THY电子制造', 'THY汽车整车', 'THY通信设备', 'THY医药制造',
            'THY银行', 'THY保险', 'THY证券', 'THY房地产开发', 'THY食品饮料',

            # 申万行业板块
            'SW计算机', 'SW电子', 'SW汽车', 'SW通信', 'SW医药生物', 'SW银行', 'SW非银金融',
            'SW房地产', 'SW食品饮料', 'SW家用电器', 'SW建筑材料', 'SW机械设备',

            # 概念板块
            'GN人工智能', 'GN新能源', 'GN军工', 'GN医药', 'GN环保', 'GN养老', 'GN教育',
            'GN体育', 'GN文化传媒', 'GN旅游', 'GN农业', 'GN消费电子',

            # 地域板块
            'DY北京', 'DY上海', 'DY深圳', 'DY广东', 'DY江苏', 'DY浙江', 'DY山东',
            'DY四川', 'DY湖北', 'DY湖南', 'DY河南', 'DY安徽',

            # 证监会行业板块
            'CSRC信息技术', 'CSRC金融业', 'CSRC制造业', 'CSRC房地产业', 'CSRC批发零售业',
            'CSRC交通运输', 'CSRC建筑业', 'CSRC采矿业', 'CSRC电力热力', 'CSRC水利环境',

            # 港股申万行业板块
            'HKSW科技硬件', 'HKSW软件服务', 'HKSW电信服务', 'HKSW媒体娱乐', 'HKSW金融服务'
        ]

    def _getMockConstituents(self, sector):
        """获取模拟成分股数据"""
        # 根据板块返回不同的模拟数据
        if '人工智能' in sector or 'AI' in sector:
            return [
                ['002230.SZ', '科大讯飞'], ['300059.SZ', '东方财富'], ['002415.SZ', '海康威视'],
                ['000063.SZ', '中兴通讯'], ['002241.SZ', '歌尔股份'], ['300496.SZ', '中科创达'],
                ['002410.SZ', '广联达'], ['300253.SZ', '卫宁健康'], ['002405.SZ', '四维图新'],
                ['300454.SZ', '深信服']
            ]
        elif '芯片' in sector:
            return [
                ['002415.SZ', '海康威视'], ['000725.SZ', '京东方A'], ['002230.SZ', '科大讯飞'],
                ['300750.SZ', '宁德时代'], ['002049.SZ', '紫光国微'], ['300661.SZ', '圣邦股份'],
                ['002371.SZ', '北方华创'], ['300782.SZ', '卓胜微'], ['688981.SH', '中芯国际'],
                ['002156.SZ', '通富微电']
            ]
        elif '新能源汽车' in sector:
            return [
                ['300750.SZ', '宁德时代'], ['002594.SZ', '比亚迪'], ['300014.SZ', '亿纬锂能'],
                ['002460.SZ', '赣锋锂业'], ['300207.SZ', '欣旺达'], ['002812.SZ', '恩捷股份'],
                ['300073.SZ', '当升科技'], ['002709.SZ', '天赐材料'], ['300568.SZ', '星源材质'],
                ['002466.SZ', '天齐锂业']
            ]
        elif '5G' in sector:
            return [
                ['000063.SZ', '中兴通讯'], ['600050.SH', '中国联通'], ['000876.SZ', '新希望'],
                ['002415.SZ', '海康威视'], ['300136.SZ', '信维通信'], ['002241.SZ', '歌尔股份'],
                ['300408.SZ', '三环集团'], ['002185.SZ', '华天科技'], ['300782.SZ', '卓胜微'],
                ['300474.SZ', '景嘉微']
            ]
        elif '计算机' in sector:
            return [
                ['002230.SZ', '科大讯飞'], ['300059.SZ', '东方财富'], ['002410.SZ', '广联达'],
                ['300253.SZ', '卫宁健康'], ['002405.SZ', '四维图新'], ['300454.SZ', '深信服'],
                ['300496.SZ', '中科创达'], ['002153.SZ', '石基信息'], ['300033.SZ', '同花顺'],
                ['300188.SZ', '美亚柏科']
            ]
        elif '电子' in sector:
            return [
                ['002415.SZ', '海康威视'], ['000725.SZ', '京东方A'], ['002241.SZ', '歌尔股份'],
                ['300136.SZ', '信维通信'], ['300408.SZ', '三环集团'], ['002185.SZ', '华天科技'],
                ['300782.SZ', '卓胜微'], ['300474.SZ', '景嘉微'], ['002049.SZ', '紫光国微'],
                ['300661.SZ', '圣邦股份']
            ]
        elif '医药' in sector:
            return [
                ['000858.SZ', '五粮液'], ['300015.SZ', '爱尔眼科'], ['000661.SZ', '长春高新'],
                ['300760.SZ', '迈瑞医疗'], ['300142.SZ', '沃森生物'], ['300347.SZ', '泰格医药'],
                ['300122.SZ', '智飞生物'], ['300601.SZ', '康泰生物'], ['300529.SZ', '健帆生物'],
                ['300003.SZ', '乐普医疗']
            ]
        elif '银行' in sector:
            return [
                ['000001.SZ', '平安银行'], ['600000.SH', '浦发银行'], ['600036.SH', '招商银行'],
                ['000002.SZ', '万科A'], ['601166.SH', '兴业银行'], ['600016.SH', '民生银行'],
                ['601328.SH', '交通银行'], ['601398.SH', '工商银行'], ['601939.SH', '建设银行'],
                ['601288.SH', '农业银行']
            ]
        elif '深圳' in sector:
            return [
                ['000001.SZ', '平安银行'], ['000002.SZ', '万科A'], ['000858.SZ', '五粮液'],
                ['002415.SZ', '海康威视'], ['000725.SZ', '京东方A'], ['002230.SZ', '科大讯飞'],
                ['300059.SZ', '东方财富'], ['300750.SZ', '宁德时代'], ['002594.SZ', '比亚迪'],
                ['000063.SZ', '中兴通讯'], ['002241.SZ', '歌尔股份'], ['300014.SZ', '亿纬锂能'],
                ['002460.SZ', '赣锋锂业'], ['300207.SZ', '欣旺达'], ['002812.SZ', '恩捷股份']
            ]
        elif '上海' in sector:
            return [
                ['600000.SH', '浦发银行'], ['600036.SH', '招商银行'], ['600050.SH', '中国联通'],
                ['601166.SH', '兴业银行'], ['600016.SH', '民生银行'], ['601328.SH', '交通银行'],
                ['601398.SH', '工商银行'], ['601939.SH', '建设银行'], ['601288.SH', '农业银行'],
                ['688981.SH', '中芯国际']
            ]
        elif '北京' in sector:
            return [
                ['000876.SZ', '新希望'], ['300136.SZ', '信维通信'], ['300408.SZ', '三环集团'],
                ['002185.SZ', '华天科技'], ['300782.SZ', '卓胜微'], ['300474.SZ', '景嘉微'],
                ['002049.SZ', '紫光国微'], ['300661.SZ', '圣邦股份'], ['002371.SZ', '北方华创'],
                ['002156.SZ', '通富微电']
            ]
        else:
            # 默认返回一些常见股票
            return [
                ['000001.SZ', '平安银行'], ['000002.SZ', '万科A'], ['600000.SH', '浦发银行'],
                ['600036.SH', '招商银行'], ['000858.SZ', '五粮液'], ['002415.SZ', '海康威视'],
                ['000725.SZ', '京东方A'], ['002230.SZ', '科大讯飞'], ['300059.SZ', '东方财富'],
                ['300750.SZ', '宁德时代']
            ]
    
    def _filterSectors(self, sectors, keywords=None, category=None):
        """过滤板块列表"""
        filtered = sectors
        
        # 按关键字过滤
        if keywords:
            filtered = [s for s in filtered if any(kw in s for kw in keywords)]
        
        # 按分类过滤
        if category:
            filtered = [s for s in filtered if s.startswith(category)]
        
        return filtered
    
    def _isCacheValid(self, cache_key):
        """检查缓存是否有效"""
        try:
            if cache_key == 'sectors':
                return (self._sectorCache.get('lastUpdate') and
                       (datetime.now() - self._sectorCache['lastUpdate']).total_seconds() < self._cacheExpiry and
                       'sectors' in self._sectorCache and
                       'all' in self._sectorCache['sectors'])
            else:
                return ('constituents' in self._sectorCache and
                       cache_key in self._sectorCache['constituents'])
        except Exception:
            return False
    
    def _updateSectorInfo(self, sectors):
        """更新板块基本信息到数据库"""
        try:
            sector_info = []
            
            for sector in sectors:
                # 解析板块分类
                category = self._parseSectorCategory(sector)
                
                info = {
                    'code': sector,
                    'name': sector,
                    'category': category,
                    'updateTime': datetime.now()
                }
                sector_info.append(info)
            
            # 保存到数据库
            self._mongoDbEngine.saveSectorInfo(sector_info)
            
        except Exception as ex:
            self._info.print(f'更新板块信息异常: {ex}', DyLogData.error)
    
    def _updateSectorConstituents(self, sectors):
        """更新板块成分股到数据库"""
        try:
            for sector in sectors:
                constituents = self.getSectorConstituents(sector)
                
                if constituents:
                    # 保存到数据库
                    self._mongoDbEngine.saveSectorConstituents(sector, constituents)
                    
        except Exception as ex:
            self._info.print(f'更新板块成分股异常: {ex}', DyLogData.error)
    
    def _parseSectorCategory(self, sector):
        """解析板块分类"""
        for prefix, category in self._sectorCategories.items():
            if sector.startswith(prefix):
                return category
        return '其他板块'
    
    def _calculateSectorPerformance(self, sector, constituents, startDate, endDate):
        """计算板块表现"""
        try:
            # 获取成分股价格数据
            stock_codes = [item[0] for item in constituents]
            
            # 计算板块指数（等权重）
            total_return = 0
            valid_stocks = 0
            
            for code in stock_codes:
                # 获取股票价格数据
                df = self._mongoDbEngine.getOneCodeDays(
                    code, startDate, endDate, ['close'], None
                )
                
                if df is not None and len(df) >= 2:
                    start_price = df.iloc[0]['close']
                    end_price = df.iloc[-1]['close']
                    
                    if start_price > 0:
                        stock_return = (end_price - start_price) / start_price
                        total_return += stock_return
                        valid_stocks += 1
            
            if valid_stocks > 0:
                avg_return = total_return / valid_stocks
                
                return {
                    'sector': sector,
                    'return': avg_return,
                    'stock_count': len(constituents),
                    'valid_stocks': valid_stocks,
                    'start_date': startDate,
                    'end_date': endDate
                }
            
            return None
            
        except Exception as ex:
            self._info.print(f'计算板块表现异常: {ex}', DyLogData.error)
            return None
    
    def _initCache(self):
        """初始化缓存"""
        try:
            # 确保缓存结构完整
            if 'sectors' not in self._sectorCache:
                self._sectorCache['sectors'] = {}

            if 'constituents' not in self._sectorCache:
                self._sectorCache['constituents'] = {}

            if 'lastUpdate' not in self._sectorCache:
                self._sectorCache['lastUpdate'] = None

            # 预加载一些常用板块到缓存
            self._preloadCommonSectors()

        except Exception as ex:
            self._info.print(f'初始化缓存异常: {ex}', DyLogData.error)

    def _preloadCommonSectors(self):
        """预加载常用板块到缓存"""
        try:
            # 如果缓存为空，预加载模拟数据
            if not self._sectorCache['sectors']:
                self._sectorCache['sectors']['all'] = self._getMockSectorList()
                self._sectorCache['lastUpdate'] = datetime.now()
                self._info.print('预加载板块列表到缓存', DyLogData.ind)

            # 预加载一些常用板块的成分股
            common_sectors = ['TGN人工智能', 'TGN芯片', 'TGN新能源汽车', 'DY深圳', 'DY上海', 'DY北京']

            for sector in common_sectors:
                cache_key = f'constituents_{sector}'
                if cache_key not in self._sectorCache['constituents']:
                    self._sectorCache['constituents'][cache_key] = self._getMockConstituents(sector)
                    self._info.print(f'预加载板块[{sector}]成分股到缓存', DyLogData.ind)

        except Exception as ex:
            self._info.print(f'预加载常用板块异常: {ex}', DyLogData.warning)

    def _clearCache(self):
        """清除缓存"""
        self._sectorCache = {
            'sectors': {},
            'constituents': {},
            'lastUpdate': None
        }
        # 重新初始化缓存
        self._initCache()
    
    def getSectorCategories(self):
        """获取板块分类"""
        return self._sectorCategories
    
    def searchSectors(self, keyword):
        """搜索板块"""
        try:
            all_sectors = self.getSectorList()
            
            # 模糊搜索
            matched_sectors = [s for s in all_sectors if keyword.lower() in s.lower()]
            
            return matched_sectors
            
        except Exception as ex:
            self._info.print(f'搜索板块异常: {ex}', DyLogData.error)
            return []
