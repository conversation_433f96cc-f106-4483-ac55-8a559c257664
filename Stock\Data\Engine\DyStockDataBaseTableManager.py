"""
基础表管理器
负责交易日表(tradeDayTableXTquant)和股票代码表(codeTableXTquant)的完整性检查和维护
确保所有数据操作的基础依赖正确
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from datetime import datetime, timedelta
import pandas as pd


class DyStockDataBaseTableManager(object):
    """基础表管理器"""
    
    def __init__(self, mongoDbEngine, gateway, info):
        """
        @param mongoDbEngine: MongoDB引擎
        @param gateway: 数据网关
        @param info: 信息输出对象
        """
        self._mongoDbEngine = mongoDbEngine
        self._gateway = gateway
        self._info = info
        
        # 基础表检查配置
        self._checkConfig = {
            'trade_days_check_range': 365,  # 检查最近365天的交易日
            'code_table_min_count': 3000,   # 股票代码表最少股票数量
            'trade_days_min_count': 200,    # 交易日表最少交易日数量
            'max_missing_recent_days': 5,   # 最近缺失交易日的最大容忍数
        }
        
        # 缓存最后检查时间，避免频繁检查
        self._lastCheckTime = {
            'tradeDayTable': None,
            'codeTable': None
        }
        
        # 检查间隔（秒）
        self._checkInterval = 300  # 5分钟检查一次
    
    def ensureBaseTablesReady(self, forceCheck=False):
        """
        确保基础表准备就绪
        @param forceCheck: 是否强制检查，忽略时间间隔
        @return: bool 是否准备就绪
        """
        try:
            self._info.print('检查基础表完整性...', DyLogData.ind)
            
            # 检查交易日表
            if not self._checkTradeDayTable(forceCheck):
                self._info.print('交易日表检查失败', DyLogData.error)
                return False
            
            # 检查股票代码表
            if not self._checkCodeTable(forceCheck):
                self._info.print('股票代码表检查失败', DyLogData.error)
                return False
            
            self._info.print('基础表检查完成，数据完整', DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f'基础表检查异常: {ex}', DyLogData.error)
            return False
    
    def _checkTradeDayTable(self, forceCheck=False):
        """检查交易日表完整性"""
        try:
            # 检查是否需要跳过（基于时间间隔）
            if not forceCheck and self._shouldSkipCheck('tradeDayTable'):
                return True
            
            self._info.print('检查交易日表完整性...', DyLogData.ind)
            
            # 获取当前日期
            today = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=self._checkConfig['trade_days_check_range'])).strftime('%Y-%m-%d')
            
            # 检查交易日表是否存在
            if not self._mongoDbEngine.isTradeDayTableExisting():
                self._info.print('交易日表不存在，开始创建...', DyLogData.warning)
                return self._rebuildTradeDayTable(start_date, today)
            
            # 检查交易日数据完整性
            trade_days = self._mongoDbEngine.getTradeDays(start_date, today)
            if not trade_days:
                self._info.print('交易日表为空，开始重建...', DyLogData.warning)
                return self._rebuildTradeDayTable(start_date, today)
            
            # 检查交易日数量是否合理
            if len(trade_days) < self._checkConfig['trade_days_min_count']:
                self._info.print(f'交易日数量不足({len(trade_days)})，开始补充...', DyLogData.warning)
                return self._updateTradeDayTable(start_date, today)
            
            # 检查最近的交易日是否缺失
            recent_missing = self._checkRecentTradeDays(trade_days, today)
            if recent_missing > self._checkConfig['max_missing_recent_days']:
                self._info.print(f'最近缺失交易日过多({recent_missing}天)，开始更新...', DyLogData.warning)
                return self._updateTradeDayTable(start_date, today)
            
            # 更新检查时间
            self._lastCheckTime['tradeDayTable'] = datetime.now()
            self._info.print('交易日表检查通过', DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f'交易日表检查异常: {ex}', DyLogData.error)
            return False
    
    def _checkCodeTable(self, forceCheck=False):
        """检查股票代码表完整性"""
        try:
            # 检查是否需要跳过（基于时间间隔）
            if not forceCheck and self._shouldSkipCheck('codeTable'):
                return True
            
            self._info.print('检查股票代码表完整性...', DyLogData.ind)
            
            # 检查股票代码表是否存在
            if not self._mongoDbEngine.isCodeTableExisting():
                self._info.print('股票代码表不存在，开始创建...', DyLogData.warning)
                return self._rebuildCodeTable()
            
            # 检查股票代码数量
            stock_codes = self._mongoDbEngine.getStockCodes()
            if not stock_codes:
                self._info.print('股票代码表为空，开始重建...', DyLogData.warning)
                return self._rebuildCodeTable()
            
            # 统一处理股票代码格式
            if isinstance(stock_codes[0], dict):
                code_count = len(stock_codes)
            else:
                code_count = len(stock_codes)
            
            # 检查股票数量是否合理
            if code_count < self._checkConfig['code_table_min_count']:
                self._info.print(f'股票代码数量不足({code_count})，开始更新...', DyLogData.warning)
                return self._updateCodeTable()
            
            # 检查代码表的时效性（检查是否有最近上市的股票）
            if not self._checkCodeTableFreshness(stock_codes):
                self._info.print('股票代码表可能过时，开始更新...', DyLogData.warning)
                return self._updateCodeTable()
            
            # 更新检查时间
            self._lastCheckTime['codeTable'] = datetime.now()
            self._info.print('股票代码表检查通过', DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f'股票代码表检查异常: {ex}', DyLogData.error)
            return False
    
    def _shouldSkipCheck(self, tableType):
        """判断是否应该跳过检查（基于时间间隔）"""
        last_check = self._lastCheckTime.get(tableType)
        if last_check is None:
            return False
        
        elapsed = (datetime.now() - last_check).total_seconds()
        return elapsed < self._checkInterval
    
    def _checkRecentTradeDays(self, trade_days, today):
        """检查最近的交易日缺失情况"""
        try:
            # 获取最近10个工作日
            recent_days = []
            current_date = datetime.strptime(today, '%Y-%m-%d')
            
            for i in range(10):
                check_date = current_date - timedelta(days=i)
                # 跳过周末
                if check_date.weekday() < 5:  # 0-4 是周一到周五
                    recent_days.append(check_date.strftime('%Y-%m-%d'))
            
            # 检查这些日期中有多少不在交易日表中
            missing_count = 0
            for day in recent_days:
                if day not in trade_days:
                    missing_count += 1
            
            return missing_count
            
        except Exception:
            return 0
    
    def _checkCodeTableFreshness(self, stock_codes):
        """检查股票代码表的时效性"""
        try:
            # 简单检查：如果代码表中有最近的股票代码格式，认为是新的
            # 这里可以根据实际情况调整检查逻辑
            
            # 统一处理股票代码格式
            if isinstance(stock_codes[0], dict):
                codes = [stock['code'] for stock in stock_codes]
            else:
                codes = stock_codes
            
            # 检查是否有北交所股票（8开头）- 这是比较新的
            bse_stocks = [code for code in codes if code.startswith('8') and '.BJ' in code]
            
            # 如果有北交所股票，认为代码表比较新
            if bse_stocks:
                return True
            
            # 检查创业板股票数量（3开头）
            cyb_stocks = [code for code in codes if code.startswith('3') and '.SZ' in code]
            
            # 如果创业板股票数量合理，认为代码表比较完整
            return len(cyb_stocks) > 100
            
        except Exception:
            return False
    
    def _rebuildTradeDayTable(self, startDate, endDate):
        """重建交易日表"""
        try:
            self._info.print(f'重建交易日表: {startDate} 到 {endDate}', DyLogData.ind)
            
            # 调用网关获取交易日数据
            if hasattr(self._gateway, 'updateTradeDays'):
                return self._gateway.updateTradeDays(startDate, endDate)
            else:
                self._info.print('网关不支持交易日更新', DyLogData.error)
                return False
                
        except Exception as ex:
            self._info.print(f'重建交易日表异常: {ex}', DyLogData.error)
            return False
    
    def _updateTradeDayTable(self, startDate, endDate):
        """更新交易日表"""
        try:
            self._info.print(f'更新交易日表: {startDate} 到 {endDate}', DyLogData.ind)
            
            # 调用网关更新交易日数据
            if hasattr(self._gateway, 'updateTradeDays'):
                return self._gateway.updateTradeDays(startDate, endDate)
            else:
                self._info.print('网关不支持交易日更新', DyLogData.error)
                return False
                
        except Exception as ex:
            self._info.print(f'更新交易日表异常: {ex}', DyLogData.error)
            return False
    
    def _rebuildCodeTable(self):
        """重建股票代码表"""
        try:
            self._info.print('重建股票代码表', DyLogData.ind)
            
            # 调用网关获取股票代码数据
            if hasattr(self._gateway, 'updateStockCodes'):
                return self._gateway.updateStockCodes()
            else:
                self._info.print('网关不支持股票代码更新', DyLogData.error)
                return False
                
        except Exception as ex:
            self._info.print(f'重建股票代码表异常: {ex}', DyLogData.error)
            return False
    
    def _updateCodeTable(self):
        """更新股票代码表"""
        try:
            self._info.print('更新股票代码表', DyLogData.ind)
            
            # 调用网关更新股票代码数据
            if hasattr(self._gateway, 'updateStockCodes'):
                return self._gateway.updateStockCodes()
            else:
                self._info.print('网关不支持股票代码更新', DyLogData.error)
                return False
                
        except Exception as ex:
            self._info.print(f'更新股票代码表异常: {ex}', DyLogData.error)
            return False
    
    def getBaseTableStatus(self):
        """
        获取基础表状态信息
        @return: dict 包含基础表状态
        """
        try:
            status = {
                'tradeDayTable': {
                    'exists': self._mongoDbEngine.isTradeDayTableExisting(),
                    'lastCheck': self._lastCheckTime.get('tradeDayTable'),
                    'recordCount': 0,
                    'latestDate': None
                },
                'codeTable': {
                    'exists': self._mongoDbEngine.isCodeTableExisting(),
                    'lastCheck': self._lastCheckTime.get('codeTable'),
                    'recordCount': 0,
                    'latestUpdate': None
                }
            }
            
            # 获取交易日表详细信息
            if status['tradeDayTable']['exists']:
                today = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
                trade_days = self._mongoDbEngine.getTradeDays(start_date, today)
                if trade_days:
                    status['tradeDayTable']['recordCount'] = len(trade_days)
                    status['tradeDayTable']['latestDate'] = max(trade_days)
            
            # 获取股票代码表详细信息
            if status['codeTable']['exists']:
                stock_codes = self._mongoDbEngine.getStockCodes()
                if stock_codes:
                    status['codeTable']['recordCount'] = len(stock_codes)
            
            return status
            
        except Exception as ex:
            self._info.print(f'获取基础表状态异常: {ex}', DyLogData.error)
            return None
    
    def forceUpdateBaseTables(self):
        """
        强制更新基础表
        @return: bool 是否成功
        """
        try:
            self._info.print('强制更新基础表...', DyLogData.ind)
            
            # 强制更新交易日表
            today = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            
            if not self._updateTradeDayTable(start_date, today):
                self._info.print('强制更新交易日表失败', DyLogData.error)
                return False
            
            # 强制更新股票代码表
            if not self._updateCodeTable():
                self._info.print('强制更新股票代码表失败', DyLogData.error)
                return False
            
            # 清除检查时间缓存，强制下次检查
            self._lastCheckTime = {'tradeDayTable': None, 'codeTable': None}
            
            self._info.print('强制更新基础表完成', DyLogData.ind)
            return True
            
        except Exception as ex:
            self._info.print(f'强制更新基础表异常: {ex}', DyLogData.error)
            return False
