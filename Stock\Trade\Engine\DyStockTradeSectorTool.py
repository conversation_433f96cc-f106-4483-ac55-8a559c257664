"""
交易模式下的板块数据工具链
提供板块相关的实时交易功能和数据读取工具
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ...Data.DyStockDataCommon import *
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class DyStockTradeSectorTool(object):
    """交易板块工具"""
    
    def __init__(self, dataEngine, info):
        """
        @param dataEngine: 数据引擎
        @param info: 信息输出对象
        """
        self._dataEngine = dataEngine
        self._info = info
        self._sectorManager = dataEngine.sectorManager
        
        # 实时数据缓存
        self._realtimeCache = {}
        self._cacheExpiry = 60  # 缓存60秒
    
    def getSectorRealTimeData(self, sectors):
        """
        获取板块实时数据
        @param sectors: 板块列表
        @return: dict 实时数据
        """
        try:
            realtime_data = {}
            
            for sector in sectors:
                # 检查缓存
                cache_key = f'realtime_{sector}'
                if self._isCacheValid(cache_key):
                    realtime_data[sector] = self._realtimeCache[cache_key]
                    continue
                
                # 获取板块实时数据
                sector_data = self._calculateSectorRealTimeData(sector)
                
                if sector_data:
                    realtime_data[sector] = sector_data
                    # 更新缓存
                    self._realtimeCache[cache_key] = sector_data
                    self._realtimeCache[f'{cache_key}_time'] = datetime.now()
            
            return realtime_data
            
        except Exception as ex:
            self._info.print(f'获取板块实时数据异常: {ex}', DyLogData.error)
            return {}
    
    def getSectorHotSpots(self, count=10):
        """
        获取板块热点
        @param count: 返回数量
        @return: list 热点板块列表
        """
        try:
            # 获取所有板块列表
            all_sectors = self._sectorManager.getSectorList()
            
            if not all_sectors:
                return []
            
            # 计算板块热度
            sector_heat = []
            
            for sector in all_sectors[:50]:  # 限制数量提高性能
                heat_score = self._calculateSectorHeat(sector)
                
                if heat_score > 0:
                    sector_heat.append((sector, heat_score))
            
            # 按热度排序
            sector_heat.sort(key=lambda x: x[1], reverse=True)
            
            return sector_heat[:count]
            
        except Exception as ex:
            self._info.print(f'获取板块热点异常: {ex}', DyLogData.error)
            return []
    
    def getSectorFlowData(self, sectors, period='1d'):
        """
        获取板块资金流向数据
        @param sectors: 板块列表
        @param period: 统计周期
        @return: dict 资金流向数据
        """
        try:
            flow_data = {}
            
            for sector in sectors:
                constituents = self._sectorManager.getSectorConstituents(sector)
                
                if not constituents:
                    continue
                
                # 计算板块资金流向
                sector_flow = self._calculateSectorFlow(constituents, period)
                
                if sector_flow:
                    flow_data[sector] = sector_flow
            
            return flow_data
            
        except Exception as ex:
            self._info.print(f'获取板块资金流向异常: {ex}', DyLogData.error)
            return {}
    
    def getSectorStrengthRanking(self, sectors):
        """
        获取板块强度排名
        @param sectors: 板块列表
        @return: list 排名列表
        """
        try:
            strength_data = []
            
            for sector in sectors:
                strength = self._calculateSectorStrength(sector)
                
                if strength is not None:
                    strength_data.append({
                        'sector': sector,
                        'strength': strength['score'],
                        'change_pct': strength['change_pct'],
                        'volume_ratio': strength['volume_ratio'],
                        'active_stocks': strength['active_stocks']
                    })
            
            # 按强度排序
            strength_data.sort(key=lambda x: x['strength'], reverse=True)
            
            return strength_data
            
        except Exception as ex:
            self._info.print(f'获取板块强度排名异常: {ex}', DyLogData.error)
            return []
    
    def monitorSectorBreakout(self, sectors, threshold=0.03):
        """
        监控板块突破
        @param sectors: 板块列表
        @param threshold: 突破阈值
        @return: list 突破板块列表
        """
        try:
            breakout_sectors = []
            
            for sector in sectors:
                breakout_signal = self._checkSectorBreakout(sector, threshold)
                
                if breakout_signal:
                    breakout_sectors.append({
                        'sector': sector,
                        'signal': breakout_signal['signal'],
                        'strength': breakout_signal['strength'],
                        'volume_confirm': breakout_signal['volume_confirm'],
                        'timestamp': datetime.now()
                    })
            
            return breakout_sectors
            
        except Exception as ex:
            self._info.print(f'监控板块突破异常: {ex}', DyLogData.error)
            return []
    
    def getSectorTradingSignals(self, sectors, strategy='momentum'):
        """
        获取板块交易信号
        @param sectors: 板块列表
        @param strategy: 策略类型
        @return: dict 交易信号
        """
        try:
            signals = {}
            
            for sector in sectors:
                if strategy == 'momentum':
                    signal = self._generateMomentumSignal(sector)
                elif strategy == 'mean_reversion':
                    signal = self._generateMeanReversionSignal(sector)
                elif strategy == 'breakout':
                    signal = self._generateBreakoutSignal(sector)
                else:
                    signal = None
                
                if signal:
                    signals[sector] = signal
            
            return signals
            
        except Exception as ex:
            self._info.print(f'获取板块交易信号异常: {ex}', DyLogData.error)
            return {}
    
    def _calculateSectorRealTimeData(self, sector):
        """计算板块实时数据"""
        try:
            constituents = self._sectorManager.getSectorConstituents(sector)
            
            if not constituents:
                return None
            
            # 获取成分股实时数据
            stock_codes = [item[0] for item in constituents[:20]]  # 限制数量
            
            total_change = 0
            total_volume = 0
            total_amount = 0
            valid_count = 0
            up_count = 0
            down_count = 0
            
            for code in stock_codes:
                # 获取实时行情数据
                tick_data = self._dataEngine.getFullTick([code])
                
                if tick_data and code in tick_data:
                    tick = tick_data[code]
                    
                    # 计算涨跌幅
                    if tick.get('preClose', 0) > 0:
                        change_pct = (tick.get('lastPrice', 0) - tick.get('preClose', 0)) / tick.get('preClose', 0)
                        total_change += change_pct
                        
                        if change_pct > 0:
                            up_count += 1
                        elif change_pct < 0:
                            down_count += 1
                    
                    # 累计成交量和成交额
                    total_volume += tick.get('volume', 0)
                    total_amount += tick.get('amount', 0)
                    
                    valid_count += 1
            
            if valid_count > 0:
                return {
                    'sector': sector,
                    'avg_change_pct': total_change / valid_count,
                    'total_volume': total_volume,
                    'total_amount': total_amount,
                    'up_count': up_count,
                    'down_count': down_count,
                    'flat_count': valid_count - up_count - down_count,
                    'valid_stocks': valid_count,
                    'timestamp': datetime.now()
                }
            
            return None
            
        except Exception as ex:
            return None
    
    def _calculateSectorHeat(self, sector):
        """计算板块热度"""
        try:
            constituents = self._sectorManager.getSectorConstituents(sector)
            
            if not constituents:
                return 0
            
            # 获取成分股数据
            stock_codes = [item[0] for item in constituents[:10]]  # 限制数量
            
            heat_score = 0
            
            for code in stock_codes:
                # 获取实时数据
                tick_data = self._dataEngine.getFullTick([code])
                
                if tick_data and code in tick_data:
                    tick = tick_data[code]
                    
                    # 计算热度因子
                    volume_factor = min(tick.get('volume', 0) / 1000000, 10)  # 成交量因子
                    amount_factor = min(tick.get('amount', 0) / 100000000, 10)  # 成交额因子
                    
                    # 价格变动因子
                    if tick.get('preClose', 0) > 0:
                        price_factor = abs((tick.get('lastPrice', 0) - tick.get('preClose', 0)) / tick.get('preClose', 0)) * 100
                    else:
                        price_factor = 0
                    
                    stock_heat = volume_factor * 0.4 + amount_factor * 0.4 + price_factor * 0.2
                    heat_score += stock_heat
            
            return heat_score / len(stock_codes) if stock_codes else 0
            
        except Exception as ex:
            return 0
    
    def _calculateSectorFlow(self, constituents, period):
        """计算板块资金流向"""
        try:
            total_inflow = 0
            total_outflow = 0
            
            for code, name in constituents[:20]:  # 限制数量
                # 获取资金流向数据（这里需要实际的资金流向数据源）
                # 暂时使用成交额和价格变动来估算
                
                tick_data = self._dataEngine.getFullTick([code])
                
                if tick_data and code in tick_data:
                    tick = tick_data[code]
                    
                    amount = tick.get('amount', 0)
                    
                    if tick.get('preClose', 0) > 0:
                        change_pct = (tick.get('lastPrice', 0) - tick.get('preClose', 0)) / tick.get('preClose', 0)
                        
                        if change_pct > 0:
                            total_inflow += amount * change_pct
                        else:
                            total_outflow += amount * abs(change_pct)
            
            net_flow = total_inflow - total_outflow
            
            return {
                'inflow': total_inflow,
                'outflow': total_outflow,
                'net_flow': net_flow,
                'flow_ratio': total_inflow / (total_inflow + total_outflow) if (total_inflow + total_outflow) > 0 else 0.5
            }
            
        except Exception as ex:
            return None
    
    def _calculateSectorStrength(self, sector):
        """计算板块强度"""
        try:
            constituents = self._sectorManager.getSectorConstituents(sector)
            
            if not constituents:
                return None
            
            # 获取板块实时数据
            sector_data = self._calculateSectorRealTimeData(sector)
            
            if not sector_data:
                return None
            
            # 计算强度评分
            change_score = sector_data['avg_change_pct'] * 100  # 涨跌幅评分
            volume_score = min(sector_data['total_volume'] / 100000000, 10)  # 成交量评分
            active_score = (sector_data['up_count'] + sector_data['down_count']) / sector_data['valid_stocks'] * 10  # 活跃度评分
            
            strength_score = change_score * 0.5 + volume_score * 0.3 + active_score * 0.2
            
            return {
                'score': strength_score,
                'change_pct': sector_data['avg_change_pct'],
                'volume_ratio': sector_data['total_volume'] / 100000000,
                'active_stocks': sector_data['up_count'] + sector_data['down_count']
            }
            
        except Exception as ex:
            return None
    
    def _checkSectorBreakout(self, sector, threshold):
        """检查板块突破"""
        try:
            # 获取板块历史数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=20)).strftime('%Y-%m-%d')
            
            # 这里需要实现突破检测逻辑
            # 暂时返回None
            return None
            
        except Exception as ex:
            return None
    
    def _generateMomentumSignal(self, sector):
        """生成动量信号"""
        try:
            # 获取板块实时数据
            sector_data = self._calculateSectorRealTimeData(sector)
            
            if not sector_data:
                return None
            
            # 简单的动量信号
            if sector_data['avg_change_pct'] > 0.02:  # 涨幅超过2%
                return {
                    'signal': 'BUY',
                    'strength': min(sector_data['avg_change_pct'] * 50, 1.0),
                    'reason': '板块动量强劲'
                }
            elif sector_data['avg_change_pct'] < -0.02:  # 跌幅超过2%
                return {
                    'signal': 'SELL',
                    'strength': min(abs(sector_data['avg_change_pct']) * 50, 1.0),
                    'reason': '板块动量疲弱'
                }
            
            return None
            
        except Exception as ex:
            return None
    
    def _generateMeanReversionSignal(self, sector):
        """生成均值回归信号"""
        # 这里需要实现均值回归信号逻辑
        return None
    
    def _generateBreakoutSignal(self, sector):
        """生成突破信号"""
        # 这里需要实现突破信号逻辑
        return None
    
    def _isCacheValid(self, cache_key):
        """检查缓存是否有效"""
        time_key = f'{cache_key}_time'
        
        if cache_key in self._realtimeCache and time_key in self._realtimeCache:
            elapsed = (datetime.now() - self._realtimeCache[time_key]).total_seconds()
            return elapsed < self._cacheExpiry
        
        return False
