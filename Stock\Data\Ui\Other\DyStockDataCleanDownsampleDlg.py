from PyQt5.QtWidgets import *
from PyQt5.QtCore import *


class DyStockDataCleanDownsampleDlg(QDialog):
    """清理降采样数据对话框"""
    
    def __init__(self, data, parent=None):
        super().__init__(parent)
        
        self._data = data
        self._initUi()

    def _initUi(self):
        self.setWindowTitle('清理降采样数据')
        self.setWindowModality(Qt.WindowModal)
        
        # 控件
        optionLabel = QLabel('选择清理选项:')
        
        # 单选按钮组
        self._radioGroup = QButtonGroup()
        
        # 清理所有数据
        self._allRadio = QRadioButton('清理所有降采样数据')
        self._allRadio.setChecked(True)
        self._radioGroup.addButton(self._allRadio, 0)
        
        # 清理指定周期
        self._specificRadio = QRadioButton('清理指定周期:')
        self._radioGroup.addButton(self._specificRadio, 1)
        
        # 周期选择下拉框
        self._periodCombo = QComboBox()
        periods = ['3分钟', '5分钟', '10分钟', '15分钟', '30分钟', '60分钟']
        self._periodCombo.addItems(periods)
        self._periodCombo.setEnabled(False)
        
        # 连接信号
        self._specificRadio.toggled.connect(self._onSpecificToggled)
        
        # 警告标签
        warningLabel = QLabel('⚠️ 警告：此操作不可恢复，请谨慎操作！')
        warningLabel.setStyleSheet('color: red; font-weight: bold;')
        
        # 按钮
        cancelPushButton = QPushButton('取消')
        okPushButton = QPushButton('确认')
        cancelPushButton.clicked.connect(self._cancel)
        okPushButton.clicked.connect(self._ok)
        
        # 布局
        mainLayout = QVBoxLayout()
        
        # 选项标签
        mainLayout.addWidget(optionLabel)
        
        # 单选按钮
        mainLayout.addWidget(self._allRadio)
        
        # 指定周期选项
        specificLayout = QHBoxLayout()
        specificLayout.addWidget(self._specificRadio)
        specificLayout.addWidget(self._periodCombo)
        specificLayout.addStretch()
        mainLayout.addLayout(specificLayout)
        
        # 警告
        mainLayout.addWidget(warningLabel)
        
        # 按钮
        buttonLayout = QHBoxLayout()
        buttonLayout.addWidget(cancelPushButton)
        buttonLayout.addWidget(okPushButton)
        mainLayout.addLayout(buttonLayout)
        
        self.setLayout(mainLayout)
        self.resize(350, 200)
    
    def _onSpecificToggled(self, checked):
        """指定周期选项切换"""
        self._periodCombo.setEnabled(checked)

    def _ok(self):
        """确认"""
        if self._allRadio.isChecked():
            # 清理所有数据
            self._data['period'] = None
        else:
            # 清理指定周期
            period_text = self._periodCombo.currentText()
            period = int(period_text.replace('分钟', ''))
            self._data['period'] = period
        
        self.accept()

    def _cancel(self):
        """取消"""
        self.reject()
