"""
选股模式下的板块数据工具链
提供板块相关的选股功能和数据读取工具
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ...Data.DyStockDataCommon import *
import pandas as pd
from datetime import datetime, timedelta


class DyStockSelectSectorTool(object):
    """选股板块工具"""
    
    def __init__(self, dataEngine, info):
        """
        @param dataEngine: 数据引擎
        @param info: 信息输出对象
        """
        self._dataEngine = dataEngine
        self._info = info
        self._sectorManager = dataEngine.sectorManager
    
    def getSectorStocks(self, sectors, filters=None):
        """
        获取板块股票列表
        @param sectors: 板块列表或单个板块
        @param filters: 过滤条件
        @return: list 股票代码列表
        """
        try:
            if isinstance(sectors, str):
                sectors = [sectors]
            
            all_stocks = set()
            
            for sector in sectors:
                constituents = self._sectorManager.getSectorConstituents(sector)
                
                if constituents:
                    stock_codes = [item[0] for item in constituents]
                    all_stocks.update(stock_codes)
            
            stock_list = list(all_stocks)
            
            # 应用过滤条件
            if filters:
                stock_list = self._applyFilters(stock_list, filters)
            
            self._info.print(f'板块[{",".join(sectors)}]共获取{len(stock_list)}只股票', DyLogData.ind)
            
            return stock_list
            
        except Exception as ex:
            self._info.print(f'获取板块股票异常: {ex}', DyLogData.error)
            return []
    
    def getSectorLeaders(self, sector, count=10, period='1d'):
        """
        获取板块龙头股票
        @param sector: 板块名称
        @param count: 返回数量
        @param period: 统计周期
        @return: list [(code, name, score), ...] 龙头股票列表
        """
        try:
            constituents = self._sectorManager.getSectorConstituents(sector)
            
            if not constituents:
                return []
            
            # 计算龙头股评分
            leaders = []
            
            for code, name in constituents:
                score = self._calculateLeaderScore(code, period)
                
                if score > 0:
                    leaders.append((code, name, score))
            
            # 按评分排序
            leaders.sort(key=lambda x: x[2], reverse=True)
            
            return leaders[:count]
            
        except Exception as ex:
            self._info.print(f'获取板块龙头异常: {ex}', DyLogData.error)
            return []
    
    def getSectorRotation(self, sectors, days=30):
        """
        获取板块轮动信息
        @param sectors: 板块列表
        @param days: 统计天数
        @return: DataFrame 板块轮动数据
        """
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # 获取板块表现
            performance_df = self._sectorManager.getSectorPerformance(
                sectors, start_date, end_date
            )
            
            if performance_df.empty:
                return pd.DataFrame()
            
            # 计算轮动指标
            performance_df['rank'] = performance_df['return'].rank(ascending=False)
            performance_df['momentum'] = performance_df['return'] * 100  # 转换为百分比
            
            # 添加强度分类
            performance_df['strength'] = performance_df['momentum'].apply(
                lambda x: '强势' if x > 5 else ('中性' if x > -5 else '弱势')
            )
            
            return performance_df.sort_values('rank')
            
        except Exception as ex:
            self._info.print(f'获取板块轮动异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def getSectorCorrelation(self, sectors, days=60):
        """
        获取板块相关性
        @param sectors: 板块列表
        @param days: 统计天数
        @return: DataFrame 相关性矩阵
        """
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # 获取各板块的日收益率
            sector_returns = {}
            
            for sector in sectors:
                returns = self._getSectorDailyReturns(sector, start_date, end_date)
                if returns is not None:
                    sector_returns[sector] = returns
            
            if not sector_returns:
                return pd.DataFrame()
            
            # 构建收益率DataFrame
            returns_df = pd.DataFrame(sector_returns)
            
            # 计算相关性矩阵
            correlation_matrix = returns_df.corr()
            
            return correlation_matrix
            
        except Exception as ex:
            self._info.print(f'获取板块相关性异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def filterStocksBySector(self, stocks, include_sectors=None, exclude_sectors=None):
        """
        按板块过滤股票
        @param stocks: 股票列表
        @param include_sectors: 包含的板块
        @param exclude_sectors: 排除的板块
        @return: list 过滤后的股票列表
        """
        try:
            filtered_stocks = []
            
            for stock in stocks:
                stock_sectors = self._sectorManager.getSectorsByStock(stock)
                
                # 检查包含条件
                if include_sectors:
                    if not any(sector in stock_sectors for sector in include_sectors):
                        continue
                
                # 检查排除条件
                if exclude_sectors:
                    if any(sector in stock_sectors for sector in exclude_sectors):
                        continue
                
                filtered_stocks.append(stock)
            
            return filtered_stocks
            
        except Exception as ex:
            self._info.print(f'按板块过滤股票异常: {ex}', DyLogData.error)
            return stocks
    
    def getSectorTrend(self, sector, days=20):
        """
        获取板块趋势
        @param sector: 板块名称
        @param days: 统计天数
        @return: dict 趋势信息
        """
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # 获取板块日收益率
            returns = self._getSectorDailyReturns(sector, start_date, end_date)
            
            if returns is None or len(returns) < 5:
                return {}
            
            # 计算趋势指标
            recent_returns = returns[-5:]  # 最近5天
            avg_return = returns.mean()
            volatility = returns.std()
            
            # 判断趋势方向
            if recent_returns.mean() > 0.01:
                trend = '上升'
            elif recent_returns.mean() < -0.01:
                trend = '下降'
            else:
                trend = '震荡'
            
            return {
                'sector': sector,
                'trend': trend,
                'avg_return': avg_return,
                'volatility': volatility,
                'recent_performance': recent_returns.mean(),
                'days': days
            }
            
        except Exception as ex:
            self._info.print(f'获取板块趋势异常: {ex}', DyLogData.error)
            return {}
    
    def _applyFilters(self, stocks, filters):
        """应用过滤条件"""
        try:
            filtered = stocks
            
            # 市值过滤
            if 'market_cap' in filters:
                min_cap, max_cap = filters['market_cap']
                filtered = self._filterByMarketCap(filtered, min_cap, max_cap)
            
            # 行业过滤
            if 'industries' in filters:
                filtered = self._filterByIndustries(filtered, filters['industries'])
            
            # ST股过滤
            if filters.get('exclude_st', False):
                filtered = [s for s in filtered if not s.startswith('ST') and '*ST' not in s]
            
            return filtered
            
        except Exception as ex:
            self._info.print(f'应用过滤条件异常: {ex}', DyLogData.error)
            return stocks
    
    def _calculateLeaderScore(self, code, period):
        """计算龙头股评分"""
        try:
            # 获取股票基本数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            df = self._dataEngine.getOneCodeDays(
                code, start_date, end_date, ['close', 'volume', 'amount'], None
            )
            
            if df is None or len(df) < 10:
                return 0
            
            # 计算评分因子
            # 1. 价格表现
            price_return = (df.iloc[-1]['close'] - df.iloc[0]['close']) / df.iloc[0]['close']
            
            # 2. 成交量活跃度
            avg_volume = df['volume'].mean()
            volume_score = min(avg_volume / 1000000, 10)  # 标准化到0-10
            
            # 3. 成交金额
            avg_amount = df['amount'].mean()
            amount_score = min(avg_amount / 100000000, 10)  # 标准化到0-10
            
            # 综合评分
            score = price_return * 40 + volume_score * 30 + amount_score * 30
            
            return max(score, 0)
            
        except Exception as ex:
            return 0
    
    def _getSectorDailyReturns(self, sector, start_date, end_date):
        """获取板块日收益率"""
        try:
            constituents = self._sectorManager.getSectorConstituents(sector)
            
            if not constituents:
                return None
            
            # 获取成分股数据
            stock_codes = [item[0] for item in constituents[:20]]  # 限制数量提高性能
            
            daily_returns = []
            
            # 获取交易日列表
            trade_days = self._dataEngine.getTradeDays(start_date, end_date)
            
            if not trade_days:
                return None
            
            for i in range(1, len(trade_days)):
                prev_day = trade_days[i-1]
                curr_day = trade_days[i]
                
                sector_return = self._calculateSectorDayReturn(
                    stock_codes, prev_day, curr_day
                )
                
                if sector_return is not None:
                    daily_returns.append(sector_return)
            
            return pd.Series(daily_returns) if daily_returns else None
            
        except Exception as ex:
            return None
    
    def _calculateSectorDayReturn(self, stock_codes, prev_day, curr_day):
        """计算板块单日收益率"""
        try:
            total_return = 0
            valid_count = 0
            
            for code in stock_codes:
                # 获取前一日收盘价
                prev_df = self._dataEngine.getOneCodeDays(
                    code, prev_day, prev_day, ['close'], None
                )
                
                # 获取当日收盘价
                curr_df = self._dataEngine.getOneCodeDays(
                    code, curr_day, curr_day, ['close'], None
                )
                
                if (prev_df is not None and not prev_df.empty and 
                    curr_df is not None and not curr_df.empty):
                    
                    prev_close = prev_df.iloc[0]['close']
                    curr_close = curr_df.iloc[0]['close']
                    
                    if prev_close > 0:
                        stock_return = (curr_close - prev_close) / prev_close
                        total_return += stock_return
                        valid_count += 1
            
            return total_return / valid_count if valid_count > 0 else None
            
        except Exception:
            return None
    
    def _filterByMarketCap(self, stocks, min_cap, max_cap):
        """按市值过滤"""
        # 这里需要实现市值过滤逻辑
        # 暂时返回原列表
        return stocks
    
    def _filterByIndustries(self, stocks, industries):
        """按行业过滤"""
        # 这里需要实现行业过滤逻辑
        # 暂时返回原列表
        return stocks
