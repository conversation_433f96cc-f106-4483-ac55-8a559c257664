"""
回测模式下的板块数据工具链
提供板块相关的回测功能和数据读取工具
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ...Data.DyStockDataCommon import *
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class DyStockBackTestingSectorTool(object):
    """回测板块工具"""
    
    def __init__(self, dataEngine, info):
        """
        @param dataEngine: 数据引擎
        @param info: 信息输出对象
        """
        self._dataEngine = dataEngine
        self._info = info
        self._sectorManager = dataEngine.sectorManager
    
    def getSectorHistoricalData(self, sector, start_date, end_date, indicators=None):
        """
        获取板块历史数据
        @param sector: 板块名称
        @param start_date: 开始日期
        @param end_date: 结束日期
        @param indicators: 指标列表
        @return: DataFrame 板块历史数据
        """
        try:
            constituents = self._sectorManager.getSectorConstituents(sector)
            
            if not constituents:
                return pd.DataFrame()
            
            # 获取交易日列表
            trade_days = self._dataEngine.getTradeDays(start_date, end_date)
            
            if not trade_days:
                return pd.DataFrame()
            
            # 构建板块指数数据
            sector_data = []
            stock_codes = [item[0] for item in constituents]
            
            for trade_day in trade_days:
                day_data = self._calculateSectorDayData(
                    stock_codes, trade_day, indicators
                )
                
                if day_data:
                    day_data['date'] = trade_day
                    day_data['sector'] = sector
                    sector_data.append(day_data)
            
            if sector_data:
                df = pd.DataFrame(sector_data)
                df.set_index('date', inplace=True)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as ex:
            self._info.print(f'获取板块历史数据异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def getSectorRelativeStrength(self, sector, benchmark='000001.SH', start_date=None, end_date=None, period=20):
        """
        获取板块相对强度
        @param sector: 板块名称
        @param benchmark: 基准指数
        @param start_date: 开始日期
        @param end_date: 结束日期
        @param period: 计算周期
        @return: DataFrame 相对强度数据
        """
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=period*2)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            # 获取板块数据
            sector_df = self.getSectorHistoricalData(sector, start_date, end_date, ['close'])
            
            # 获取基准数据
            benchmark_df = self._dataEngine.getOneCodeDays(
                benchmark, start_date, end_date, ['close'], None
            )
            
            if sector_df.empty or benchmark_df is None or benchmark_df.empty:
                return pd.DataFrame()
            
            # 计算相对强度
            sector_df['benchmark_close'] = benchmark_df['close']
            sector_df['sector_return'] = sector_df['close'].pct_change()
            sector_df['benchmark_return'] = sector_df['benchmark_close'].pct_change()
            sector_df['relative_strength'] = sector_df['sector_return'] - sector_df['benchmark_return']
            
            # 计算移动平均相对强度
            sector_df[f'rs_ma{period}'] = sector_df['relative_strength'].rolling(period).mean()
            
            return sector_df
            
        except Exception as ex:
            self._info.print(f'获取板块相对强度异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def backtestSectorRotation(self, sectors, start_date, end_date, rebalance_freq='M'):
        """
        回测板块轮动策略
        @param sectors: 板块列表
        @param start_date: 开始日期
        @param end_date: 结束日期
        @param rebalance_freq: 再平衡频率 ('D', 'W', 'M')
        @return: dict 回测结果
        """
        try:
            # 获取所有板块的历史数据
            sector_data = {}
            
            for sector in sectors:
                df = self.getSectorHistoricalData(sector, start_date, end_date, ['close'])
                if not df.empty:
                    sector_data[sector] = df['close']
            
            if not sector_data:
                return {}
            
            # 构建价格矩阵
            price_df = pd.DataFrame(sector_data)
            price_df = price_df.dropna()
            
            if price_df.empty:
                return {}
            
            # 计算收益率
            returns_df = price_df.pct_change().dropna()
            
            # 执行轮动策略
            strategy_returns = self._executeRotationStrategy(
                returns_df, price_df, rebalance_freq
            )
            
            # 计算策略表现
            performance = self._calculateStrategyPerformance(strategy_returns)
            
            return {
                'returns': strategy_returns,
                'performance': performance,
                'sectors': sectors,
                'period': f'{start_date} to {end_date}'
            }
            
        except Exception as ex:
            self._info.print(f'回测板块轮动异常: {ex}', DyLogData.error)
            return {}
    
    def getSectorMomentum(self, sectors, start_date, end_date, lookback_period=20):
        """
        获取板块动量数据
        @param sectors: 板块列表
        @param start_date: 开始日期
        @param end_date: 结束日期
        @param lookback_period: 回望期
        @return: DataFrame 动量数据
        """
        try:
            momentum_data = []
            
            for sector in sectors:
                df = self.getSectorHistoricalData(sector, start_date, end_date, ['close'])
                
                if not df.empty and len(df) > lookback_period:
                    # 计算动量指标
                    df['momentum'] = df['close'] / df['close'].shift(lookback_period) - 1
                    df['rsi'] = self._calculateRSI(df['close'], 14)
                    df['ma_ratio'] = df['close'] / df['close'].rolling(lookback_period).mean()
                    
                    # 添加板块标识
                    df['sector'] = sector
                    
                    momentum_data.append(df)
            
            if momentum_data:
                combined_df = pd.concat(momentum_data)
                return combined_df
            else:
                return pd.DataFrame()
                
        except Exception as ex:
            self._info.print(f'获取板块动量异常: {ex}', DyLogData.error)
            return pd.DataFrame()
    
    def analyzeSectorCycles(self, sectors, start_date, end_date):
        """
        分析板块周期性
        @param sectors: 板块列表
        @param start_date: 开始日期
        @param end_date: 结束日期
        @return: dict 周期分析结果
        """
        try:
            cycle_analysis = {}
            
            for sector in sectors:
                df = self.getSectorHistoricalData(sector, start_date, end_date, ['close'])
                
                if not df.empty:
                    # 计算周期性指标
                    analysis = self._analyzeSectorCycle(df, sector)
                    cycle_analysis[sector] = analysis
            
            return cycle_analysis
            
        except Exception as ex:
            self._info.print(f'分析板块周期异常: {ex}', DyLogData.error)
            return {}
    
    def _calculateSectorDayData(self, stock_codes, trade_day, indicators):
        """计算板块单日数据"""
        try:
            if not indicators:
                indicators = ['open', 'high', 'low', 'close', 'volume', 'amount']
            
            day_data = {}
            valid_stocks = 0
            
            # 初始化累计值
            for indicator in indicators:
                day_data[indicator] = 0
            
            # 累计各股票数据
            for code in stock_codes:
                stock_df = self._dataEngine.getOneCodeDays(
                    code, trade_day, trade_day, indicators, None
                )
                
                if stock_df is not None and not stock_df.empty:
                    for indicator in indicators:
                        if indicator in ['volume', 'amount']:
                            # 成交量和成交额累加
                            day_data[indicator] += stock_df.iloc[0][indicator]
                        else:
                            # 价格数据累加（后续计算平均值）
                            day_data[indicator] += stock_df.iloc[0][indicator]
                    
                    valid_stocks += 1
            
            # 计算平均价格
            if valid_stocks > 0:
                for indicator in ['open', 'high', 'low', 'close']:
                    if indicator in day_data:
                        day_data[indicator] /= valid_stocks
                
                day_data['stock_count'] = valid_stocks
                return day_data
            
            return None
            
        except Exception as ex:
            return None
    
    def _executeRotationStrategy(self, returns_df, price_df, rebalance_freq):
        """执行轮动策略"""
        try:
            # 设置再平衡日期
            rebalance_dates = self._getRebalanceDates(returns_df.index, rebalance_freq)
            
            strategy_returns = []
            current_position = None
            
            for i, date in enumerate(returns_df.index):
                if date in rebalance_dates or current_position is None:
                    # 再平衡：选择动量最强的板块
                    if i >= 20:  # 需要足够的历史数据
                        momentum_scores = returns_df.iloc[i-20:i].mean()
                        current_position = momentum_scores.idxmax()
                    else:
                        current_position = returns_df.columns[0]  # 默认第一个板块
                
                # 记录当日收益
                if current_position in returns_df.columns:
                    daily_return = returns_df.loc[date, current_position]
                    strategy_returns.append(daily_return)
                else:
                    strategy_returns.append(0)
            
            return pd.Series(strategy_returns, index=returns_df.index)
            
        except Exception as ex:
            return pd.Series()
    
    def _getRebalanceDates(self, date_index, freq):
        """获取再平衡日期"""
        try:
            if freq == 'D':
                return date_index
            elif freq == 'W':
                return date_index[date_index.weekday == 4]  # 每周五
            elif freq == 'M':
                return date_index.groupby([date_index.year, date_index.month]).max()
            else:
                return date_index[::20]  # 默认20天
                
        except Exception:
            return date_index[::20]
    
    def _calculateStrategyPerformance(self, returns):
        """计算策略表现"""
        try:
            if returns.empty:
                return {}
            
            # 计算累计收益
            cumulative_returns = (1 + returns).cumprod()
            total_return = cumulative_returns.iloc[-1] - 1
            
            # 计算年化收益率
            days = len(returns)
            annual_return = (1 + total_return) ** (252 / days) - 1
            
            # 计算波动率
            volatility = returns.std() * np.sqrt(252)
            
            # 计算夏普比率
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0
            
            # 计算最大回撤
            max_drawdown = self._calculateMaxDrawdown(cumulative_returns)
            
            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': (returns > 0).mean()
            }
            
        except Exception as ex:
            return {}
    
    def _calculateMaxDrawdown(self, cumulative_returns):
        """计算最大回撤"""
        try:
            peak = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - peak) / peak
            return drawdown.min()
        except Exception:
            return 0
    
    def _calculateRSI(self, prices, period=14):
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series(index=prices.index)
    
    def _analyzeSectorCycle(self, df, sector):
        """分析单个板块的周期性"""
        try:
            # 计算价格变化
            df['returns'] = df['close'].pct_change()
            
            # 计算移动平均
            df['ma20'] = df['close'].rolling(20).mean()
            df['ma60'] = df['close'].rolling(60).mean()
            
            # 判断趋势
            uptrend_days = (df['close'] > df['ma20']).sum()
            total_days = len(df)
            
            # 计算波动性
            volatility = df['returns'].std()
            
            # 识别周期
            cycle_length = self._identifyCycleLength(df['close'])
            
            return {
                'sector': sector,
                'uptrend_ratio': uptrend_days / total_days if total_days > 0 else 0,
                'volatility': volatility,
                'cycle_length': cycle_length,
                'total_days': total_days
            }
            
        except Exception as ex:
            return {'sector': sector, 'error': str(ex)}
    
    def _identifyCycleLength(self, prices):
        """识别周期长度"""
        try:
            # 简单的峰谷识别
            from scipy.signal import find_peaks
            
            peaks, _ = find_peaks(prices.values)
            troughs, _ = find_peaks(-prices.values)
            
            if len(peaks) > 1 and len(troughs) > 1:
                avg_peak_distance = np.mean(np.diff(peaks))
                avg_trough_distance = np.mean(np.diff(troughs))
                return int((avg_peak_distance + avg_trough_distance) / 2)
            else:
                return None
                
        except Exception:
            return None
