from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from .Common.DyStockDataCommonEngine import *
from concurrent.futures import ThreadPoolExecutor
import math
import concurrent.futures
import pandas as pd


class DyStockDataMin1Engine(object):
    """股票1分钟线数据引擎"""

    def __init__(self, eventEngine, mongoDbEngine, gateway, info, registerEvent=True):
        self._eventEngine = eventEngine
        self._mongoDbEngine = mongoDbEngine
        self._gateway = gateway
        self._info = info

        self._commonEngine = DyStockDataCommonEngine(self._mongoDbEngine, self._gateway, self._info)
        self._progress = DyProgress(self._info)

        self._updatedCodeCount = 0  # 更新1分钟数据的计数器
        self._codeMin1Df = {}  # 股票的1分钟DataFrame
        self._codeAdjFactors = {} # 股票的复权因子
        
        if registerEvent:
            self._registerEvent()

    def _loadMin1(self, startDate, endDate, indicators):
        self._info.print('开始载入{0}只股票(指数,基金)的1分钟数据[{1}, {2}]...'.format(len(self.stockAllCodesFunds), startDate, endDate))

        # init
        self._codeMin1Df = {}

        # 启用进度条显示
        self._progress.init(len(self.stockAllCodesFunds), 100, 5)

        for code, name in self.stockAllCodesFunds.items():
            df = self._mongoDbEngine.getOneCodeMin1(code, startDate, endDate, indicators, name)

            if df is not None:
                self._codeMin1Df[code] = df

            self._progress.update()

        self._info.print('股票(指数,基金)的1分钟数据载入完成')
        return True

    def _getMin1NotInDb(self, tradeDays, codes, indicators):
        """
        获取不存在的1分钟数据
        @tradeDays: [trade day]
        @codes: {code: name} or [code]
        @indicators: [indicator]
        @return: {code: {indicator: [trade day]}}
        """
        self._info.print('开始检查1分钟数据...')

        # 使用优化的批量查询方法
        return self._mongoDbEngine.batchGetNotExistingMin1Dates(codes, tradeDays, indicators)


    def _updateHistDaysBasic(self, startDate, endDate):
        """
            更新全部A股代码表,交易日数据及板块成分代码表
        """
        return self._commonEngine.updateCodes() and self._commonEngine.updateTradeDays(startDate, endDate)# and self._commonEngine.updateAllSectorCodes(startDate, endDate)

    def _getUpdatedCodes(self, startDate, endDate, indicators, isForced, codes=None):
        """
            @return: {code: {indicator: [trade day]}}
        """
        # get trade days
        tradeDays = self._commonEngine.getTradeDays(startDate, endDate)
        if not tradeDays:
            self._info.print("获取交易日期失败: [{}, {}]".format(startDate, endDate), DyLogData.error)
            return None
        # get stock codes, including indexes and funds
        codes = self.stockAllCodesFunds if codes is None else codes
        # 打印调试信息
        self._info.print("更新1分钟数据: 交易日数={}, 股票数={}, 指标数={}".format(
            len(tradeDays), len(codes), len(indicators)))
        # get not existing from DB
        if not isForced:
            codes = self._getMin1NotInDb(tradeDays, codes, indicators)
            if not codes:
                self._info.print("历史1分钟数据已经在数据库")
                self._progress.init(0)
                self._eventEngine.put(DyEvent(DyEventType.finish))
                return None
        else:
            newCodes = {}
            if tradeDays and indicators:
                for code in codes:
                    newCodes[code] = {}
                    for indicator in indicators:
                        newCodes[code][indicator] = tradeDays
            codes = newCodes
            if not codes:
                self._info.print("没有1分钟数据需要更新")
                self._progress.init(0)
                self._eventEngine.put(DyEvent(DyEventType.finish))
                return None
        # 打印更新信息
            self._info.print("将更新{}只股票的1分钟数据".format(len(codes)))
        return codes

    def _updateHistMin1(self, startDate, endDate, indicators, isForced=False, codes=None):
        # get updated codes data info
        codes = self._getUpdatedCodes(startDate, endDate, indicators, isForced, codes)
        if codes is None: return

        # init
        self._isStopped = False
        self._updatedCodeCount = 0
        self._progress.init(len(codes), 10)

        self._info.print("开始更新{0}只股票(指数,基金)的历史1分钟数据...".format(len(codes)))

        # send for updating
        event = DyEvent(DyEventType.updateStockHistMin1_)
        event.data = codes

        self._eventEngine.put(event)

    def _update(self, startDate, endDate, indicators, isForced=False, codes=None):
        # update all stock A code table and trade day table firstly
        if not self._updateHistDaysBasic(startDate, endDate):
            self._printCount()
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # 更新1分钟数据
        self._updateHistMin1(startDate, endDate, indicators, isForced, codes)

    def _autoUpdate(self):
        """
        自动更新1分钟线数据
        1. 获取最新的股票代码列表
        2. 获取数据库最新日期
        3. 更新所有股票的1分钟线数据
        """
        # 1. 首先更新股票代码表
        if not self._commonEngine.updateCodes():
            self._info.print("更新股票代码表失败", DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # 2. 获取并更新交易日数据
        latestTradeDay = self._commonEngine.getLatestTradeDayInDb()
        if latestTradeDay is None:
            # 如果没有交易日数据，从2010年开始更新
            startDate = '2010-01-01'
            endDate = datetime.now().strftime("%Y-%m-%d")
            
            self._info.print("数据库里没有交易日数据，开始从{}更新交易日数据...".format(startDate))
            if not self._commonEngine.updateTradeDays(startDate, endDate):
                self._info.print("更新交易日数据失败", DyLogData.error)
                self._eventEngine.put(DyEvent(DyEventType.fail))
                return
            
            latestTradeDay = self._commonEngine.getLatestTradeDayInDb()

        # 3. 获取数据库最新1分钟线数据日期
        latestMin1Date = self._mongoDbEngine.getLatestMin1Date()
        
        # 4. 设置结束日期为当前日期
        endDate = datetime.now().strftime("%Y-%m-%d")

        # 5. 检查是否可以更新今日数据
        ret = self._gateway.isNowAfterTradingTime()
        if ret is False: # 如果现在是交易时间
            self._info.print("今天是交易日, 请18:00后更新今日数据", DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # 6. 设置开始日期
        if latestMin1Date is None:
            # 如果数据库中没有1分钟线数据，从2010年开始更新
            startDate = '2010-01-01'
            self._info.print("数据库里没有1分钟线数据，将从{}开始更新".format(startDate))
        else:
            startDate = DyTime.getDateStr(latestMin1Date, 1)

        print('自动更新跟踪: 最新交易日={}, 最新1分钟线数据日={}, 开始日期={}, 结束日期={}'.format(
            latestTradeDay, latestMin1Date, startDate, endDate))

        # 7. 检查是否需要更新
        if endDate < startDate:  # 如果当前日期小于开始日期
            self._progress.init(0)
            self._info.print("数据库1分钟线数据已经是最新", DyLogData.ind)
            self._eventEngine.put(DyEvent(DyEventType.finish))
            return

        # 8. 获取所有股票代码
        allCodes = self.stockAllCodesFunds
        if not allCodes:
            self._info.print("没有找到任何股票代码", DyLogData.error)
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # 9. 执行更新
        # 强制更新所有股票代码的1分钟线数据
        self._update(startDate, endDate, DyStockDataCommon.min1Indicators, codes=allCodes, isForced=True)

    def _updateStockHistMin1Handler(self, event):
        self._progress.reset()

        if event.data is None:
            self._autoUpdate()
        else:
            # 检查event.data的类型
            if isinstance(event.data, dict):
                # 从字典中获取参数
                startDate = event.data.get('startDate')
                endDate = event.data.get('endDate')
                indicators = event.data.get('indicators', DyStockDataCommon.min1Indicators)
                isForced = event.data.get('forced', False)
                codes = event.data.get('codes')
            else:
                # 如果不是字典,说明是直接传入的codes
                startDate = None
                endDate = None
                indicators = DyStockDataCommon.min1Indicators
                isForced = False
                codes = event.data

            # update
            self._update(startDate, endDate, indicators, isForced, codes)

    def _stopReqHandler(self, event):
        self._isStopped = True

    def _updateStockSectorCodesHandler(self, event):
        sectorCodeList = event.data['sectorCode']
        startDate = event.data['startDate']
        endDate = event.data['endDate']

        self._progress.reset()

        for sectorCode in sectorCodeList:
            if not self._commonEngine.updateSectorCodes(sectorCode, startDate, endDate):
                self._eventEngine.put(DyEvent(DyEventType.fail))
                return
                
        self._eventEngine.put(DyEvent(DyEventType.finish))

    def _updateOneCode(self, code, data):
        # get max date range
        startDate, endDate = None, None
        for _, dates in data.items():
            if startDate is None:
                startDate = dates[0]
                endDate = dates[-1]
            else:
                if operator.lt(dates[0], startDate):
                    startDate = dates[0]

                if operator.gt(dates[-1], endDate):
                    endDate = dates[-1]

        # get from Gateway
        data = self._gateway.getMin1(code, startDate, endDate, sorted(data), self.stockAllCodesFunds[code])
        if not data:
            if data is None:
                self._info.print("获取{}({})1分钟数据[{}, {}]失败".format(code, self.stockAllCodesFunds[code], startDate, endDate), DyLogData.error)
            return

        # update to DB
        if self._mongoDbEngine.updateMin1(code, data):
            self._updatedCodeCount += 1

    def _printCount(self):
        self._info.print('由于股票停牌或者没有上市, 更新了{0}只股票(指数,基金)1分钟数据'.format(self._updatedCodeCount), DyLogData.ind)

    def _updateStockHistMin1_Handler(self, event):
        # unpack
        codes = event.data
        # print('updateStockHistMin1_Handler: {}'.format(codes))

        # check stop flag firstly
        if self._isStopped:
            self._printCount()
            self._eventEngine.put(DyEvent(DyEventType.stopAck))
            return
        # update one code each time
        code = sorted(codes)[0]
        # print('update one code: {}'.format(code))

        self._updateOneCode(code, codes[code])

        # update progress
        self._progress.update()

        # delete updated code
        del codes[code]

        if not codes:
            self._printCount()
            self._eventEngine.put(DyEvent(DyEventType.finish))
            return

        # send for next updating
        event = DyEvent(DyEventType.updateStockHistMin1_)
        event.data = codes

        self._eventEngine.put(event)

    def _loadCommon(self, dates, codes):
        if not self._commonEngine.load(dates, codes):
            return None, None

        return self._commonEngine.tOldestDay(), self._commonEngine.tLatestDay()

    def _loadAdjFactors(self, date, latestAdjFactorInDb):
        """载入复权因子"""
        self._info.print('开始载入复权因子...')
        self._codeAdjFactors = {}
        
        # 检查是否有数据需要处理
        if not self._codeMin1Df:
            self._info.print('没有需要处理的1分钟数据', DyLogData.warning)
            return True
        
        # 检查数据是否已前复权
        sample_code = list(self._codeMin1Df.keys())[0]
        df_columns = self._codeMin1Df[sample_code].columns
        
        # 如果数据中没有adjfactor字段,说明是已复权数据
        if 'adjfactor' not in df_columns:
            self._info.print('数据已前复权,无需加载复权因子')
            return True
            
        if latestAdjFactorInDb:
            date = self._commonEngine.getLatestTradeDayInDb()
            if date is None:
                return False
                
        self._progress.init(len(self._codeMin1Df), 100, 10)
        
        for code, _ in self._codeMin1Df.items():
            try:
                adjFactor = self._mongoDbEngine.getAdjFactor(code, date, self.stockAllCodesFunds[code])
                if adjFactor is not None:
                    self._codeAdjFactors[code] = adjFactor
                else:
                    # 如果获取不到复权因子,使用1作为默认值
                    self._codeAdjFactors[code] = 1
                    # self._info.print(f'{code}使用默认复权因子1', DyLogData.warning)
            except:
                # 异常情况下也使用默认值
                self._codeAdjFactors[code] = 1
                self._info.print(f'{code}获取复权因子异常,使用默认值1', DyLogData.warning)
                
            self._progress.update()
            
        return True

    def _processAdj(self):
        """前复权处理"""
        self._info.print("开始前复权...")
        
        # 检查是否有数据需要处理
        if not self._codeMin1Df:
            self._info.print("没有需要处理的1分钟数据", DyLogData.warning)
            return
        
        # 检查是否需要前复权
        sample_code = list(self._codeMin1Df.keys())[0]
        if 'adjfactor' not in self._codeMin1Df[sample_code].columns:
            self._info.print("数据已前复权,无需处理")
            return
            
        self._progress.init(len(self._codeMin1Df), 100, 20)
    
        for code, df in self._codeMin1Df.items():
            # 复权因子变换
            adjFactor = df['adjfactor']/self._codeAdjFactors[code]
            adjFactor = adjFactor.values.reshape((adjFactor.shape[0], 1))

            # 价格相关
            prices = df[['open', 'high', 'low', 'close']].values  
            df[['open', 'high', 'low', 'close']] = prices * adjFactor

            # 成交量
            df[['volume']] = df[['volume']].values / adjFactor

            self._progress.update()

        self._info.print("前复权完成")

    def _unionDates(self, startDate, endDate, dates):
        """合并日期范围"""
        for date in dates:
            if isinstance(date, str):
                if operator.lt(date, startDate):
                    startDate = date

                if operator.gt(date, endDate):
                    endDate = date

        return startDate, endDate

    def _loadOneCodeMin1(self, code, dates, indicators):
        """
            载入个股1分钟数据,个股对应的指数数据也被载入
            个股上市可能早于指数
        """
        # 载入个股1分钟数据
        df = self._mongoDbEngine.getOneCodeMin1Unified(code, dates, indicators, self.stockAllCodesFundsSectors[code])
        if df is None:
            return None, None

        # init
        self._codeMin1Df = {}

        # set stock DF
        self._codeMin1Df[code] = df

        # new days
        startDay = df.index[0].strftime("%Y-%m-%d")
        endDay = df.index[-1].strftime("%Y-%m-%d")

        # 载入对应的指数1分钟数据
        index = self.getIndex(code)
        df = self._mongoDbEngine.getOneCodeMin1(index, startDay, endDay, indicators, self.stockIndexes[index])

        #!!! 个股上市可能早于指数
        if df is not None:
            self._codeMin1Df[index] = df

        # 获取日期并集for trade days loading
        return self._unionDates(startDay, endDay, dates)

    def _registerEvent(self):
        self._eventEngine.register(DyEventType.updateStockHistMin1, self._updateStockHistMin1Handler, DyStockDataEventHandType.min1Engine)
        self._eventEngine.register(DyEventType.updateStockHistMin1_, self._updateStockHistMin1_Handler, DyStockDataEventHandType.min1Engine)
        self._eventEngine.register(DyEventType.stopUpdateStockHistMin1Req, self._stopReqHandler, DyStockDataEventHandType.min1Engine)
        self._eventEngine.register(DyEventType.updateStockSectorCodes, self._updateStockSectorCodesHandler, DyStockDataEventHandType.min1Engine)
     
    ####################################################
    # -------------------- 公共接口 --------------------
    ####################################################
    def test(self):
        pass

    def tDaysOffset(self, base, n=0):
        return self._commonEngine.tDaysOffset(base, n)

    def tDays(self, start, end):
        return self._commonEngine.tDays(start, end)
     
    def tLatestDay(self):
        return self._commonEngine.tLatestDay()

    def tLatestDayInDb(self):
        return self._commonEngine.getLatestTradeDayInDb()

    def tDaysCountInDb(self, start, end):
        return self._commonEngine.tDaysCountInDb(start, end)

    def tDaysOffsetInDb(self, base, n=0):
        return self._commonEngine.tDaysOffsetInDb(base, n)

    def codeTDayOffset(self, code, baseDate, n=0, strict=True):
        """ 根据偏移获取个股的交易日
            @strict: 严格方式,非严格方式,则获取股票在数据库里的最大偏移
        """
        return self._mongoDbEngine.codeTDayOffset(code, baseDate, n, strict)

    def getCode(self, name):
        """
            根据股票名称获取股票代码
        """
        return self._commonEngine.getCode(name)

    @property
    def eventEngine(self):
        return self._eventEngine

    @property
    def shIndex(self):
        return self._commonEngine.shIndex

    @property
    def szIndex(self):
        return self._commonEngine.szIndex

    @property
    def cybIndex(self):
        return self._commonEngine.cybIndex

    @property
    def zxbIndex(self):
        return self._commonEngine.zxbIndex

    @property
    def etf50(self):
        return self._commonEngine.etf50

    @property
    def etf300(self):
        return self._commonEngine.etf300

    @property
    def etf500(self):
        return self._commonEngine.etf500

    @property
    def stockFunds(self):
        return self._commonEngine.stockFunds

    @property
    def stockCodesFunds(self):
        return self._commonEngine.stockCodesFunds

    @property
    def stockAllCodesFunds(self):
        return self._commonEngine.stockAllCodesFunds

    @property
    def stockAllCodesFundsSectors(self):
        return self._commonEngine.stockAllCodesFundsSectors

    @property
    def stockCodes(self):
        return self._commonEngine.stockCodes

    @property
    def stockAllCodes(self):
        return self._commonEngine.stockAllCodes

    @property
    def stockIndexes(self):
        """
            大盘指数
        """
        return self._commonEngine.stockIndexes

    @property
    def stockIndexesSectors(self):
        """
            大盘指数和板块指数
        """
        return self._commonEngine.stockIndexesSectors

    def getIndex(self, code):
        """
            获取个股对应的大盘指数
        """
        return self._commonEngine.getIndex(code)

    def getIndexStockCodes(self, index=None):
        """
            获取大盘指数包含的股票代码表
        """
        return self._commonEngine.getIndexStockCodes(index)

    def getIndexSectorStockCodes(self, index=None):
        """
            获取大盘指数或者板块指数包含的股票代码表
        """
        return self._commonEngine.getIndexSectorStockCodes(index)

    def loadCodeTable(self, codes=None):
        return self._commonEngine.loadCodeTable(codes)

    def loadSectorCodeTable(self, sectorCode, date, codes=None):
        """
            载入板块的成份股代码表
        """
        return self._commonEngine.loadSectorCodeTable(sectorCode, date, codes)

    def getSectorCodes(self, sectorCode):
        """
            获取板块的成份股代码表
            call after @loadSectorCodeTable
            @return: {code: name}
        """
        return self._commonEngine.getSectorCodes(sectorCode)

    def loadTradeDays(self, dates):
        return self._commonEngine.loadTradeDays(dates)

    def loadCommon(self, dates, codes=None):
        return self._commonEngine.load(dates, codes)

    def getStockMarketDate(self, code, name=None):
        return self._mongoDbEngine.getStockMarketDate(code, name)

    def loadCode(self, code, dates, indicators=DyStockDataCommon.min1Indicators, latestAdjFactorInDb=True):
        """ 
            以个股（基金）在数据库里的数据加载1分钟数据。个股对应的指数数据默认载入
            @dates: 类型是list,有如下几种模式:
                        [startDate, endDate]
                        [baseDate, (+/-)n] 负数是向前,正数向后
                        [startDate, baseDate, +n]
                        [-n, baseDate, +n]
                        [-n, startDate, endDate]
            @latestAdjFactorInDb: True - 基于数据库最新复权因子前复权,一般用于选股分析和回归
                                  False - 基于end day的复权因子前复权,一般用于实盘回测
        """
        # 载入股票代码表
        if not self.loadCodeTable([code]):
            self._info.print('载入[{0}]股票代码表失败'.format(code), DyLogData.error)
            return False

        # 载入股票1分钟数据
        startDay, endDay = self._loadOneCodeMin1(code, dates, indicators)
        if startDay is None:
            self._info.print('载入[{0}:{1}]1分钟数据失败{2}'.format(code, self.stockAllCodesFundsSectors[code], dates), DyLogData.error)
            return False

        # 载入交易日数据
        if not self.loadTradeDays([startDay, endDay]):
            self._info.print('载入交易日数据失败', DyLogData.error)
            return False

        # 载入复权因子
        if not self._loadAdjFactors(endDay, latestAdjFactorInDb):
            self._info.print('载入复权因子失败', DyLogData.error)
            return False

        # 前复权
        self._processAdj()

        return True

    def load(self, dates, indicators=DyStockDataCommon.min1Indicators, codes=None):
        """
            载入1分钟数据
            @dates: 类型是list,有如下几种模式:
                        [startDate, endDate]
                        [baseDate, (+/-)n] 负数是向前,正数向后
                        [startDate, baseDate, +n]
                        [-n, baseDate, +n]
                        [-n, startDate, endDate]
        """
        # 载入公共数据
        startDay, endDay = self._loadCommon(dates, codes)
        if startDay is None:
            self._info.print('载入公共数据失败', DyLogData.error)
            return False

        # 载入1分钟数据
        if not self._loadMin1(startDay, endDay, indicators):
            
            self._info.print('载入1分钟数据失败', DyLogData.error)
            return False
        return True

    def getDataFrame(self, code, date=None, n=None):
        """
            获取DataFrame
            @date: 数据日期, 如果没有指定, 则返回所有数据
            @n: 向前/向后偏移的数据个数
        """
        df = self._codeMin1Df.get(code)
        if df is None:
            return None

        if date is None:
            return df

        if isinstance(n, int):
            # convert to absloute dates
            endDay = self.tDaysOffset(date, 0)
            startDay = self.tDaysOffset(date, n)

            if n > 0:
                startDay, endDay = endDay, startDay
        else:
            startDay, endDay = self.tDaysOffset(date, 0), n

        # !!!当只有一行数据的时候,会导致环切切不到
        retDf = None
        if df.shape[0] == 1:
            if startDay == endDay and startDay in df.index:
                retDf = df

        if retDf is None:
            retDf = df[startDay:endDay]

        return retDf

    def isExisting(self, code, date):
        """
            @date: 数据日期
            @return: bool
        """
        if code not in self._codeMin1Df:
            return False

        try:
            self._codeMin1Df[code].loc[date]
        except Exception as ex:
            return False

        return True 

    def downsampleMin1Data(self, code, interval, startDate, endDate):
        """
        将数据库1分钟数据中指定股票代码的数据降采样为指定的分钟数（单位：分钟）
        采样规则为：日内降采样，不跨日，每个交易日内：
            open 取每个桶内第一个数据，
            high 取最大值，
            low 取最小值，
            close 取最后一个数据，
            volume与amt分别累加

        @param code: 股票代码
        @param interval: 降采样的分钟数（例如5代表5分钟一根）
        @param startDate: 起始日期，格式YYYY-MM-DD
        @param endDate: 结束日期，格式YYYY-MM-DD
        @return: 降采样后的DataFrame，索引为时间戳；若数据为空则返回None
        """
        # 获取1分钟数据，尝试根据代码与日期区间获取数据
        # 尝试从stockAllCodesFunds中获取股票名称，如果未定义则使用股票代码
        stock_name = self.stockAllCodesFunds.get(code, code) if hasattr(self, "stockAllCodesFunds") else code
        df = self._mongoDbEngine.getOneCodeMin1(code, startDate, endDate, 
               DyStockDataCommon.min1Indicators, stock_name)
        if df is None or df.empty:
            self._info.print(f"股票[{code}]在[{startDate}, {endDate}]区间内无1分钟数据", DyLogData.warning)
            return None
        
        # 确保索引为DatetimeIndex
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'datetime' in df.columns:
                df.index = pd.to_datetime(df['datetime'])
            else:
                df.index = pd.to_datetime(df.index)
        
        # 直接使用索引归一化按天分组，避免额外添加"date"列
        df = df.sort_index()
        resampled_list = []
        # 构造聚合规则：open取第一个；high取最大；low取最小；close取最后；volume与amt求和
        agg_dict = {}
        for col in ['open', 'high', 'low', 'close']:
            if col in df.columns:
                agg_dict[col] = {'open':'first', 'high':'max', 'low':'min', 'close':'last'}[col]
        if 'volume' in df.columns:
            agg_dict['volume'] = 'sum'
        if 'amt' in df.columns:
            agg_dict['amt'] = 'sum'
        
        # 对每个交易日分别进行降采样，利用index.normalize()直接分组
        for day, group in df.groupby(df.index.normalize()):
            # group已按索引排序，无需内部再次排序
            r = group.resample(f'{interval}T', closed='left', label='left').agg(agg_dict)
            # 如果r中open全部为空，则跳过
            r = r.dropna(subset=['open'])
            resampled_list.append(r)
        
        if resampled_list:
            resampled_df = pd.concat(resampled_list).sort_index()
            return resampled_df
        else:
            return None

    def sampleMin1DataByVolume(self, code, volumeThreshold, startDate, endDate):
        """
        对数据库中1分钟数据中指定股票代码的数据按照指定累计成交量阈值进行采样，采样规则为日内聚合：
            - 在每个交易日内按照时间顺序遍历数据，累计"volume"；
            - 当累计成交量达到或超过 volumeThreshold 时，将这一批数据聚合为一个采样周期：
                  open  取该周期首个数据的开盘价，
                  high  取该周期内最高价，
                  low   取该周期内最低价，
                  close 取该周期内最后一笔数据的收盘价，
                  volume 累计成交量（求和），
                  amt   （如果存在）累计成交额（求和）；
            - 如果一日结束时剩余数据的累计量不足阈值，也作为一个采样周期输出。
        @param code: 股票代码
        @param volumeThreshold: 累计成交量阈值（例如 1000000）
        @param startDate: 起始日期，格式YYYY-MM-DD
        @param endDate: 结束日期，格式YYYY-MM-DD
        @return: 采样后的DataFrame，索引为采样周期内首个数据的时间，包含open、high、low、close、volume以及amt（若存在）
        """
        import pandas as pd

        # 获取1分钟原始数据
        stock_name = self.stockAllCodesFunds.get(code, code) if hasattr(self, "stockAllCodesFunds") else code
        df = self._mongoDbEngine.getOneCodeMin1(code, startDate, endDate, DyStockDataCommon.min1Indicators, stock_name)
        if df is None or df.empty:
            self._info.print(f"股票[{code}]在[{startDate}, {endDate}]区间内无1分钟数据", DyLogData.warning)
            return None

        # 确保索引为DatetimeIndex
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'datetime' in df.columns:
                df.index = pd.to_datetime(df['datetime'])
            else:
                df.index = pd.to_datetime(df.index)

        # 按索引排序，并以日期归一化进行分组（确保不跨日处理）
        df = df.sort_index()
        aggregated_list = []
        
        for day, group in df.groupby(df.index.normalize()):
            group = group.sort_index()
            cumulative_volume = 0
            temp_rows = []  # 用于存储当前采样周期内的行 (timestamp, row)
            for timestamp, row in group.iterrows():
                temp_rows.append((timestamp, row))
                cumulative_volume += row.get('volume', 0)
                if cumulative_volume >= volumeThreshold:
                    # 聚合当前采样周期内数据
                    agg_open = temp_rows[0][1]['open']
                    agg_high = max(r['high'] for _, r in temp_rows if 'high' in r)
                    agg_low = min(r['low'] for _, r in temp_rows if 'low' in r)
                    agg_close = temp_rows[-1][1]['close']
                    agg_volume = sum(r.get('volume', 0) for _, r in temp_rows)
                    if 'amt' in group.columns:
                        agg_amt = sum(r.get('amt', 0) for _, r in temp_rows)
                    else:
                        agg_amt = None
                    agg_dict = {
                        'open': agg_open,
                        'high': agg_high,
                        'low': agg_low,
                        'close': agg_close,
                        'volume': agg_volume,
                    }
                    if agg_amt is not None:
                        agg_dict['amt'] = agg_amt
                    # 使用当前周期第一条数据的时间作为结果索引
                    aggregated_list.append(pd.DataFrame(agg_dict, index=[temp_rows[0][0]]))
                    # 重置累计值和临时存储
                    temp_rows = []
                    cumulative_volume = 0
            # 如果当前交易日仍有未满阈值的剩余数据，也输出为一个采样周期
            if temp_rows:
                agg_open = temp_rows[0][1]['open']
                agg_high = max(r['high'] for _, r in temp_rows if 'high' in r)
                agg_low = min(r['low'] for _, r in temp_rows if 'low' in r)
                agg_close = temp_rows[-1][1]['close']
                agg_volume = sum(r.get('volume', 0) for _, r in temp_rows)
                if 'amt' in group.columns:
                    agg_amt = sum(r.get('amt', 0) for _, r in temp_rows)
                else:
                    agg_amt = None
                agg_dict = {
                    'open': agg_open,
                    'high': agg_high,
                    'low': agg_low,
                    'close': agg_close,
                    'volume': agg_volume,
                }
                if agg_amt is not None:
                    agg_dict['amt'] = agg_amt
                aggregated_list.append(pd.DataFrame(agg_dict, index=[temp_rows[0][0]]))
        
        if aggregated_list:
            result_df = pd.concat(aggregated_list).sort_index()
            return result_df
        else:
            return None

