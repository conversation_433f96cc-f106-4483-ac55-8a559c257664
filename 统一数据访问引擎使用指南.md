# 统一数据访问引擎使用指南

## 📋 概述

统一数据访问引擎（DyStockDataUnifiedEngine）为DevilYuan提供了一个统一的接口来访问所有类型的股票数据，包括日线、1分钟、5分钟和财务数据。

## 🚀 快速开始

### 基本初始化

```python
from EventEngine.DyEventEngine import DyEventEngine
from Stock.Data.Engine.DyStockDataEngine import DyStockDataEngine

# 创建事件引擎和数据引擎
eventEngine = DyEventEngine()
dataEngine = DyStockDataEngine(eventEngine, info)

# 获取统一数据访问引擎
unifiedEngine = dataEngine.unifiedEngine
```

### 支持的数据类型

```python
# 获取所有支持的数据类型
dataTypes = unifiedEngine.getSupportedDataTypes()
print(dataTypes)  # ['days', 'min1', 'min5', 'financial']

# 获取每种数据类型的默认指标
for dataType in dataTypes:
    indicators = unifiedEngine.getDefaultIndicators(dataType)
    print(f"{dataType}: {len(indicators)} 个指标")
```

## 📊 数据加载

### 加载单个股票数据

```python
# 加载日线数据
success = unifiedEngine.loadData(
    dataType='days',
    code='000001',
    dates=['2024-12-01', '2024-12-19']
)

# 加载1分钟数据
success = unifiedEngine.loadData(
    dataType='min1',
    code='000001',
    dates=['2024-12-19']
)

# 加载5分钟数据
success = unifiedEngine.loadData(
    dataType='min5',
    code='000001',
    dates=['2024-12-19']
)

# 加载财务数据
success = unifiedEngine.loadData(
    dataType='financial',
    code='000001',
    dates=['2024-12-01', '2024-12-19']
)
```

### 加载多个股票数据

```python
# 加载所有股票的日线数据
success = unifiedEngine.loadData(
    dataType='days',
    code=None,  # None表示所有股票
    dates=['2024-12-01', '2024-12-19']
)

# 使用自定义指标
success = unifiedEngine.loadData(
    dataType='days',
    code='000001',
    dates=['2024-12-01', '2024-12-19'],
    indicators=['open', 'high', 'low', 'close', 'volume']
)
```

## 📈 数据获取

### 获取已加载的数据

```python
# 获取日线数据
df_days = unifiedEngine.getData('days', '000001')

# 获取1分钟数据
df_min1 = unifiedEngine.getData('min1', '000001')

# 获取5分钟数据
df_min5 = unifiedEngine.getData('min5', '000001')

# 获取财务数据
df_financial = unifiedEngine.getData('financial', '000001')

# 检查数据是否为空
if df_days is not None and not df_days.empty:
    print(f"获取到 {len(df_days)} 条日线数据")
```

### 直接从数据库获取数据

```python
# 直接从数据库获取日线数据
df = unifiedEngine.getDataFromDb(
    dataType='days',
    code='000001',
    startDate='2024-12-01',
    endDate='2024-12-19',
    indicators=['open', 'high', 'low', 'close', 'volume'],
    name='平安银行'
)

# 直接获取1分钟数据
df_min1 = unifiedEngine.getDataFromDb(
    dataType='min1',
    code='000001',
    startDate='2024-12-19',
    endDate='2024-12-19'
)
```

## 🔍 数据检查

### 检查数据可用性

```python
# 检查股票数据是否可用
isAvailable = unifiedEngine.isDataAvailable('days', '000001')
print(f"日线数据可用: {isAvailable}")

# 检查特定日期的数据
isAvailable = unifiedEngine.isDataAvailable('days', '000001', '2024-12-19')
print(f"2024-12-19日线数据可用: {isAvailable}")

# 批量检查多种数据类型
for dataType in ['days', 'min1', 'min5', 'financial']:
    isAvailable = unifiedEngine.isDataAvailable(dataType, '000001')
    print(f"{dataType}数据可用: {isAvailable}")
```

### 获取最新数据日期

```python
# 获取各种数据类型的最新日期
for dataType in ['days', 'min1', 'min5', 'financial']:
    latestDate = unifiedEngine.getLatestDate(dataType)
    print(f"{dataType}最新日期: {latestDate}")
```

## 🔄 数据更新

### 更新数据

```python
# 更新日线数据
success = unifiedEngine.updateData(
    dataType='days',
    startDate='2024-12-01',
    endDate='2024-12-19',
    codes=['000001', '000002'],  # 指定股票代码
    isForced=False  # 是否强制更新
)

# 更新所有股票的数据
success = unifiedEngine.updateData(
    dataType='days',
    startDate='2024-12-01',
    endDate='2024-12-19',
    codes=None,  # None表示所有股票
    isForced=True
)
```

## 🛠️ 实用工具

### 日期验证

```python
# 验证日期范围
isValid = unifiedEngine.validateDateRange('2024-12-01', '2024-12-19')
print(f"日期范围有效: {isValid}")

# 无效的日期范围
isValid = unifiedEngine.validateDateRange('2024-12-19', '2024-12-01')
print(f"日期范围有效: {isValid}")  # False
```

### 错误处理

```python
try:
    # 尝试加载数据
    success = unifiedEngine.loadData('days', '000001', ['2024-12-19'])
    
    if success:
        df = unifiedEngine.getData('days', '000001')
        if df is not None:
            print(f"成功获取 {len(df)} 条数据")
        else:
            print("数据为空")
    else:
        print("数据加载失败")
        
except Exception as e:
    print(f"操作失败: {e}")
```

## 📋 完整示例

### 股票数据分析示例

```python
def analyze_stock_data(code, start_date, end_date):
    """分析股票数据的完整示例"""
    
    # 初始化
    eventEngine = DyEventEngine()
    dataEngine = DyStockDataEngine(eventEngine, info)
    unifiedEngine = dataEngine.unifiedEngine
    
    try:
        # 1. 检查数据可用性
        print(f"检查股票 {code} 的数据可用性...")
        for dataType in ['days', 'min1', 'min5']:
            isAvailable = unifiedEngine.isDataAvailable(dataType, code)
            print(f"  {dataType}: {'可用' if isAvailable else '不可用'}")
        
        # 2. 加载日线数据
        print(f"加载日线数据...")
        success = unifiedEngine.loadData('days', code, [start_date, end_date])
        if success:
            df_days = unifiedEngine.getData('days', code)
            print(f"  获取到 {len(df_days)} 条日线数据")
        
        # 3. 加载分钟数据
        print(f"加载1分钟数据...")
        success = unifiedEngine.loadData('min1', code, [end_date])
        if success:
            df_min1 = unifiedEngine.getData('min1', code)
            print(f"  获取到 {len(df_min1)} 条1分钟数据")
        
        # 4. 数据分析
        if df_days is not None and not df_days.empty:
            print(f"数据分析结果:")
            print(f"  日期范围: {df_days.index[0]} 到 {df_days.index[-1]}")
            print(f"  最高价: {df_days['high'].max():.2f}")
            print(f"  最低价: {df_days['low'].min():.2f}")
            print(f"  平均成交量: {df_days['volume'].mean():.0f}")
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        return False
    
    finally:
        # 清理资源
        eventEngine.stop()

# 使用示例
analyze_stock_data('000001', '2024-12-01', '2024-12-19')
```

## ⚠️ 注意事项

### 1. 资源管理
- 使用完毕后记得停止事件引擎
- 大量数据加载时注意内存使用
- 定期清理不需要的数据

### 2. 错误处理
- 始终检查返回值和异常
- 网络连接问题可能导致数据加载失败
- 数据库连接问题需要重试机制

### 3. 性能优化
- 批量加载比单个加载更高效
- 使用合适的日期范围避免加载过多数据
- 缓存常用数据减少重复查询

### 4. 数据质量
- 检查数据完整性
- 注意停牌股票的数据缺失
- 验证数据的时间范围

## 🔗 相关文档

- [数据调用工具链完善报告](数据调用工具链完善报告.md)
- [策略热重载功能说明](策略热重载功能说明.md)
- [昨日涨停选股策略说明](昨日涨停选股策略说明.md)

---

**版本：** v1.0  
**更新时间：** 2024-12-19  
**适用范围：** DevilYuan数据引擎  
**兼容性：** 向后兼容
