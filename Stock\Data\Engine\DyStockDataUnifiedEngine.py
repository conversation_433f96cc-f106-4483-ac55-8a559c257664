"""
统一数据访问引擎
提供统一的接口访问日线、1分钟、5分钟和财务数据
参照日线数据的成功模式，为其他数据类型提供完整的工具链
"""

from DyCommon.DyCommon import *
from EventEngine.DyEvent import *
from ..DyStockDataCommon import *
from .Common.DyStockDataCommonEngine import *


class DyStockDataUnifiedEngine(object):
    """统一数据访问引擎"""
    
    def __init__(self, dataEngine):
        """
        @param dataEngine: DyStockDataEngine实例
        """
        self._dataEngine = dataEngine
        self._mongoDbEngine = dataEngine.mongoDbEngine
        self._info = dataEngine.info
        
        # 数据类型映射
        self._engineMap = {
            'days': dataEngine.daysEngine,
            'min1': dataEngine.min1Engine,
            'min5': dataEngine.min5Engine,
            'financial': dataEngine.financialTableEngine
        }
        
        # 指标映射
        self._indicatorMap = {
            'days': DyStockDataCommon.dayIndicators,
            'min1': DyStockDataCommon.min1Indicators,
            'min5': DyStockDataCommon.min5Indicators,
            'financial': DyStockDataCommon.financialTableIndicators
        }
    
    def loadData(self, dataType, code=None, dates=None, indicators=None, **kwargs):
        """
        统一的数据加载接口
        @param dataType: 数据类型 ('days', 'min1', 'min5', 'financial')
        @param code: 股票代码，None表示加载所有
        @param dates: 日期范围
        @param indicators: 指标列表，None使用默认指标
        @param kwargs: 其他参数
        @return: bool
        """
        if dataType not in self._engineMap:
            self._info.print(f"不支持的数据类型: {dataType}", DyLogData.error)
            return False
        
        engine = self._engineMap[dataType]
        if indicators is None:
            indicators = self._indicatorMap[dataType]
        
        try:
            if code is None:
                # 加载所有股票数据
                return engine.load(dates, indicators, **kwargs)
            else:
                # 加载单个股票数据
                return engine.loadCode(code, dates, indicators, **kwargs)
        except Exception as e:
            self._info.print(f"加载{dataType}数据失败: {e}", DyLogData.error)
            return False
    
    def getData(self, dataType, code):
        """
        获取已加载的数据
        @param dataType: 数据类型
        @param code: 股票代码
        @return: DataFrame or None
        """
        if dataType not in self._engineMap:
            return None
        
        engine = self._engineMap[dataType]
        
        try:
            if dataType == 'days':
                return engine.getDataFrame(code) if hasattr(engine, 'getDataFrame') else None
            elif dataType == 'min1':
                return engine._codeMin1Df.get(code) if hasattr(engine, '_codeMin1Df') else None
            elif dataType == 'min5':
                return engine._codeMin5Df.get(code) if hasattr(engine, '_codeMin5Df') else None
            elif dataType == 'financial':
                return engine._codeFinancialDf.get(code) if hasattr(engine, '_codeFinancialDf') else None
        except Exception as e:
            self._info.print(f"获取{dataType}数据失败: {e}", DyLogData.error)
            return None
    
    def getDataFromDb(self, dataType, code, startDate, endDate, indicators=None, name=None):
        """
        直接从数据库获取数据
        @param dataType: 数据类型
        @param code: 股票代码
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param indicators: 指标列表
        @param name: 股票名称
        @return: DataFrame or None
        """
        if indicators is None:
            indicators = self._indicatorMap.get(dataType, [])
        
        try:
            if dataType == 'days':
                return self._mongoDbEngine.getOneCodeDays(code, startDate, endDate, indicators, name)
            elif dataType == 'min1':
                return self._mongoDbEngine.getOneCodeMin1(code, startDate, endDate, indicators, name)
            elif dataType == 'min5':
                return self._mongoDbEngine.getOneCodeMin5(code, startDate, endDate, indicators, name)
            elif dataType == 'financial':
                return self._mongoDbEngine.getOneCodeFinancialTable(code, startDate, endDate, indicators, name)
            else:
                self._info.print(f"不支持的数据类型: {dataType}", DyLogData.error)
                return None
        except Exception as e:
            self._info.print(f"从数据库获取{dataType}数据失败: {e}", DyLogData.error)
            return None
    
    def isDataAvailable(self, dataType, code, date=None):
        """
        检查数据是否可用
        @param dataType: 数据类型
        @param code: 股票代码
        @param date: 日期（可选）
        @return: bool
        """
        if dataType not in self._engineMap:
            return False
        
        engine = self._engineMap[dataType]
        
        try:
            if hasattr(engine, 'isExisting'):
                return engine.isExisting(code, date) if date else engine.isExisting(code)
            else:
                # 检查数据是否已加载
                data = self.getData(dataType, code)
                return data is not None and not data.empty
        except Exception as e:
            self._info.print(f"检查{dataType}数据可用性失败: {e}", DyLogData.error)
            return False
    
    def getLatestDate(self, dataType):
        """
        获取数据库中最新的数据日期
        @param dataType: 数据类型
        @return: str or None
        """
        if dataType not in self._engineMap:
            return None
        
        engine = self._engineMap[dataType]
        
        try:
            if hasattr(engine, 'tLatestDayInDb'):
                return engine.tLatestDayInDb()
            else:
                return None
        except Exception as e:
            self._info.print(f"获取{dataType}最新日期失败: {e}", DyLogData.error)
            return None
    
    def updateData(self, dataType, startDate, endDate, codes=None, isForced=False):
        """
        更新数据
        @param dataType: 数据类型
        @param startDate: 开始日期
        @param endDate: 结束日期
        @param codes: 股票代码列表，None表示所有
        @param isForced: 是否强制更新
        @return: bool
        """
        if dataType not in self._engineMap:
            self._info.print(f"不支持的数据类型: {dataType}", DyLogData.error)
            return False
        
        engine = self._engineMap[dataType]
        indicators = self._indicatorMap[dataType]
        
        try:
            if hasattr(engine, '_update'):
                return engine._update(startDate, endDate, indicators, codes, isForced)
            else:
                self._info.print(f"{dataType}引擎不支持数据更新", DyLogData.warning)
                return False
        except Exception as e:
            self._info.print(f"更新{dataType}数据失败: {e}", DyLogData.error)
            return False
    
    def getSupportedDataTypes(self):
        """获取支持的数据类型列表"""
        return list(self._engineMap.keys())
    
    def getDefaultIndicators(self, dataType):
        """获取默认指标列表"""
        return self._indicatorMap.get(dataType, [])
    
    def validateDateRange(self, startDate, endDate):
        """
        验证日期范围
        @param startDate: 开始日期
        @param endDate: 结束日期
        @return: bool
        """
        try:
            from datetime import datetime
            start = datetime.strptime(startDate, '%Y-%m-%d')
            end = datetime.strptime(endDate, '%Y-%m-%d')
            return start <= end
        except Exception as e:
            self._info.print(f"日期格式错误: {e}", DyLogData.error)
            return False
