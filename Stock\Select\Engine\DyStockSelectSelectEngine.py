import traceback
import importlib
import sys

from ...Data.Engine.DyStockDataEngine import *
from EventEngine.DyEvent import *
from ..DyStockSelectCommon import *
from DyCommon.DyCommon import *
from ..Utility.DyStockSelectMongoDBSaver import DyStockSelectMongoDBSaver


class DyStockSelectSelectEngine(object):
    """
        选股引擎(包含指数和股票,但没有基金)
        数据（日线和Tick）是基于最新的复权因子前复权,包含价格和成交量
        基于选股的实盘策略回测和交易时,一定要做除复权的正向逆向转换
        若日线选股和Tick选股同时激活,首先先执行日线选股,然后再根据日线选股的结果载入对应的Tick数据,进行Tick选股。
        可以认为Tick选股是对日线选股的进一步细化。
    """


    def __init__(self, eventEngine, info, registerEvent=True):
        self._eventEngine = eventEngine
        self._info = info

        self._testedStocks = None

        self._init()

        self._dataEngine = DyStockDataEngine(self._eventEngine, self._info, False)
        self._progress = DyProgress(self._info)

        self._daysEngine = self._dataEngine.daysEngine

        if registerEvent:
            self._registerEvent()

        # For日线数据补全
        errorInfo = DyErrorInfo(eventEngine)
        self._errorDataEngine = DyStockDataEngine(eventEngine, errorInfo, registerEvent=False)
        self._errorDaysEngine = self._errorDataEngine.daysEngine

        # 独立于daysEnigne,因为ticksEngine载入数据时,需要载入日线数据做参照。
        # 这样可以避免先前@self._daysEngine载入的数据被污染。
        self._ticksEngine = self._errorDataEngine.ticksEngine

        # 初始化MongoDB保存工具
        self._mongoDBSaver = DyStockSelectMongoDBSaver(info)

        # 是否自动保存选股结果到MongoDB
        self._autoSaveToMongoDB = True

        # 是否自动重载策略模块（开发模式）
        self._autoReloadStrategy = True

    def _reloadStrategyModule(self, strategyCls):
        """
        重载策略模块，确保使用最新的代码
        """
        try:
            # 获取策略类的模块
            module = sys.modules.get(strategyCls.__module__)
            if module:
                self._info.print(f"重载策略模块: {strategyCls.__module__}", DyLogData.info)
                # 重载模块
                importlib.reload(module)

                # 从重载后的模块中获取更新的策略类
                updated_module = sys.modules[strategyCls.__module__]
                updated_strategyCls = getattr(updated_module, strategyCls.__name__)

                return updated_strategyCls
            else:
                self._info.print(f"未找到策略模块: {strategyCls.__module__}", DyLogData.warning)
                return strategyCls

        except Exception as e:
            self._info.print(f"重载策略模块失败: {str(e)}", DyLogData.error)
            return strategyCls

    def _smartDataPreCheck(self, strategyCls, parameters):
        """
        智能数据预检查
        根据策略需求自动检查和补全所需数据
        """
        try:
            self._info.print('开始智能数据预检查...', DyLogData.ind)

            # 检查数据引擎是否支持智能管理器
            if not hasattr(self._dataEngine, 'smartManager'):
                self._info.print('数据引擎不支持智能管理器，跳过预检查', DyLogData.warning)
                return True

            smartManager = self._dataEngine.smartManager

            # 获取策略参数
            baseDate = parameters.get('基准日期')
            histDays = parameters.get('数据周期', 30)

            if not baseDate:
                self._info.print('无法获取基准日期，跳过数据预检查', DyLogData.warning)
                return True

            # 计算数据日期范围
            from datetime import datetime, timedelta
            try:
                end_date = datetime.strptime(baseDate, '%Y-%m-%d')
                start_date = end_date - timedelta(days=histDays + 10)  # 额外增加10天缓冲

                startDateStr = start_date.strftime('%Y-%m-%d')
                endDateStr = end_date.strftime('%Y-%m-%d')
            except Exception as ex:
                self._info.print(f'日期格式错误: {ex}', DyLogData.error)
                return True

            # 获取股票列表（抽样检查）
            stock_list = self._dataEngine.mongoDbEngine.getStockCodes()
            if not stock_list:
                self._info.print('无法获取股票列表，跳过数据预检查', DyLogData.warning)
                return True

            # 统一处理股票列表格式并抽样
            if isinstance(stock_list[0], dict):
                codes = [stock['code'] for stock in stock_list]
            else:
                codes = stock_list

            # 抽样检查（选择前50只股票）
            sample_codes = codes[:min(50, len(codes))]

            # 根据策略类型确定需要检查的数据类型
            data_types = self._getRequiredDataTypes(strategyCls, parameters)

            # 检查每种数据类型
            all_success = True
            for data_type in data_types:
                self._info.print(f'检查{data_type}数据完整性...', DyLogData.ind)

                result = smartManager.ensureDataAvailable(
                    data_type, sample_codes, startDateStr, endDateStr, autoFix=True
                )

                if result['success']:
                    if result['fixed_data']:
                        self._info.print(f'{data_type}数据检查完成，自动修复了{len(result["fixed_data"])}只股票', DyLogData.ind)
                    else:
                        self._info.print(f'{data_type}数据完整，无需修复', DyLogData.ind)
                else:
                    self._info.print(f'{data_type}数据检查失败，可能影响策略运行', DyLogData.warning)
                    all_success = False

            if all_success:
                self._info.print('智能数据预检查完成，所有数据准备就绪', DyLogData.ind)
            else:
                self._info.print('智能数据预检查发现问题，但策略仍可运行', DyLogData.warning)

            return True  # 即使有问题也继续运行，让策略自己处理

        except Exception as ex:
            self._info.print(f'智能数据预检查异常: {ex}', DyLogData.error)
            return True  # 出现异常时继续运行

    def _getRequiredDataTypes(self, strategyCls, parameters):
        """
        根据策略类型确定需要的数据类型
        """
        data_types = ['days']  # 默认需要日线数据

        try:
            # 检查策略名称或类型来确定数据需求
            strategy_name = getattr(strategyCls, 'name', '')

            # 如果策略名称包含分钟相关关键词，则需要分钟数据
            if any(keyword in strategy_name.lower() for keyword in ['min', '分钟', 'minute']):
                data_types.extend(['min1', 'min5'])

            # 如果策略名称包含财务相关关键词，则需要财务数据
            if any(keyword in strategy_name.lower() for keyword in ['financial', '财务', 'fundamental']):
                data_types.append('financial')

            # 检查策略脚本内容（如果是脚本策略）
            script_content = parameters.get('指标脚本', '')
            if script_content:
                # 如果脚本中包含分钟数据相关的函数或变量，则需要分钟数据
                if any(keyword in script_content for keyword in ['MIN', 'MINUTE', '分钟']):
                    if 'min1' not in data_types:
                        data_types.append('min1')
                    if 'min5' not in data_types:
                        data_types.append('min5')

                # 如果脚本中包含财务数据相关的函数，则需要财务数据
                if any(keyword in script_content for keyword in ['FINANCE', 'FUNDAMENTAL', '财务']):
                    if 'financial' not in data_types:
                        data_types.append('financial')

        except Exception as ex:
            self._info.print(f'分析策略数据需求异常: {ex}', DyLogData.warning)

        return data_types

    def stop(self):
        self._isStopped = True

    def _init(self):
        self._isStopped = False

        self._strategy = None

        self._codes = None # [code]

        # 日线相关数据
        self._isDays = False
        self._startDay = None # 策略的开始交易日
        self._endDay = None # 策略的结束交易日

        # @DyStockSelectStrategyTemplate.autoFillDays引入的相关变量。只有@autoFillDays is True时记录。
        self._onDaysLoadDates = None # [ , ], 策略@onDaysLoad的返回值。主要是为了切片策略指定日期范围的日线数据。
        self._expectedDaysSize = None # 期望载入日线数据的大小,以多少日期算。只有@autoFillDays is True并且是策略相对载入时, 记录该值。
        self._baseDay = None

        # tick相关数据
        self._isTicks = False
        self._startTicksDay = None
        self._endTicksDay = None

        self._result = None
        self._resultForTrade = None

    def _onLoadDays(self):
        # 获取策略需要载入数据的日期范围
        startDate, endDate = self._strategy.onDaysLoad()
        if startDate is None:
            return True # 由策略控制载入数据的日期范围,也就是说策略必须要实现onLoadDays和onLoadTicks中的一个。

        # 策略需要补全缺失的日线数据,从策略返回的原始日期范围数据
        if self._strategy.autoFillDays:
            self._onDaysLoadDates = [startDate, endDate]

        self._startDay = startDate
        self._endDay = endDate

        # 相对日期载入数据, 得到[startDate, baseDate, n]序列
        if isinstance(endDate, int):

            # 策略需要补全缺失的日线数据并且是相对载入
            if self._onDaysLoadDates is not None:
                self._expectedDaysSize = abs(endDate) + 1

            if endDate < 0:
                baseDate = startDate
                startDate = endDate
                n = 0
            else:
                baseDate = startDate
                startDate = 0
                n = endDate

        else: # 绝对日期载入数据
            baseDate = endDate
            n = 0

        # 调整载入数据日期
        dates = self._strategy.onPostDaysLoad(startDate, baseDate, n)

        # 从股票数据引擎载入数据. 如果策略指定了codes,则优先codes
        if not self._daysEngine.load(dates, codes=self._testedStocks if self._codes is None else self._codes):
            return False

        # 设置日线相关数据
        self._isDays = True
        
        if isinstance(self._endDay, int):
            self._startDay = self._daysEngine.tDaysOffset(self._startDay, 0)
            self._endDay = self._daysEngine.tDaysOffset(self._startDay, self._endDay)
        else:
            self._startDay = self._daysEngine.tDaysOffset(self._startDay, 0)
            self._endDay = self._daysEngine.tDaysOffset(self._endDay, 0)

        if self._endDay < self._startDay:
            self._startDay, self._endDay = self._endDay, self._startDay

        self._baseDay = self._daysEngine.tDaysOffset(baseDate, 0)

        return True

    def _onLoadTicks(self):
        # 获取策略需要载入数据的日期范围
        startDate, endDate = self._strategy.onTicksLoad()
        if startDate is None:
            return True # 由策略控制载入数据的日期范围,也就是说策略必须要实现onLoadDays和onLoadTicks中的一个

        self._startTicksDay = startDate
        self._endTicksDay = endDate

        # 相对日期载入数据, 得到[startDate, baseDate, n]序列
        if isinstance(endDate, int):
            if endDate < 0:
                baseDate = startDate
                startDate = endDate
                n = 0
            else:
                baseDate = startDate
                startDate = 0
                n = endDate

        else: # 绝对日期载入数据
            baseDate = endDate
            n = 0

        if not self._isDays:
            # 调整载入数据日期
            dates = self._strategy.onPostDaysLoad(startDate, baseDate, n)

            # 从股票数据引擎载入日线数据,主要是为了引擎计算其它日线指标用. 如果策略指定了codes,则优先codes
            if not self._daysEngine.load(dates, codes=self._testedStocks if self._codes is None else self._codes):
                return False

        # 设置ticks相关数据
        self._isTicks = True

        # 对@self._endTicksDay不做类似日线数据的统一日期处理,这是因为日线数据的载入是所有股票统一时间周期载入。
        # 而Ticks的数据载入则是每个股票的Ticks分别载入。

        return True

    def _onLoad(self):
        # get loaded codes
        self._codes = self._strategy.onCodes()

        # days
        if not self._onLoadDays():
            return False

        # ticks
        if not self._onLoadTicks():
            return False

        return True

    def _autoFillDays(self, code, orgDf):
        """
            补全个股日线数据
            @orgDf: 原始切片日线数据
        """
        # 数据是完整的
        if orgDf is not None and orgDf.shape[0] == self._expectedDaysSize:
            return orgDf

        # 策略优化自动补全日线数据
        if self._strategy.optimizeAutoFillDays:
            if orgDf is None or self._baseDay not in orgDf.index: # 基准日期不在原始切片数据里,则没有补全的意义
                return None

        if not self._errorDaysEngine.loadCode(code, self._onDaysLoadDates):
            return orgDf

        return self._errorDaysEngine.getDataFrame(code)

    def _runDaysLoop(self):
        if not self._isDays:
            return

        self._info.print("开始运行日线数据...")

        # init progress
        self._progress.init(len(self._daysEngine.stockAllCodesFunds), 100, 5)

        # index loop
        for index in self._daysEngine.stockIndexes:
            df = self._daysEngine.getDataFrame(index, self._startDay, self._endDay)
            if df is not None:
                self._strategy.onIndexDays(index, df)

            self._progress.update()

        # ETF loop
        for etfCode in self._daysEngine.stockFunds:
            df = self._daysEngine.getDataFrame(etfCode, self._startDay, self._endDay)
            if df is not None:
                self._strategy.onEtfDays(etfCode, df)

            self._progress.update()

        # stock loop
        for code in self._daysEngine.stockCodes:
            df = self._daysEngine.getDataFrame(code, self._startDay, self._endDay)

            # 策略需要补全数据
            if self._expectedDaysSize is not None:
                df = self._autoFillDays(code, df)

            if df is not None or self._strategy.fullyPushDays:
                try:
                    self._strategy.onStockDays(code, df)
                except AssertionError:
                    raise
                except Exception as ex:
                    if DyStockSelectCommon.enableSelectEngineException:
                        self._info.print('{0}[{1}]: onStockDays异常:{2}'.format(code, self._daysEngine.stockAllCodes[code], repr(ex)), DyLogData.error)

            self._progress.update()

        self._info.print("日线数据运行完成")

    def _runTicksLoop(self):
        if not self._isTicks:
            return

        codes = self._strategy.getResultCodes() if self._isDays else self._daysEngine.stockCodes

        self._info.print("开始运行Ticks数据,总共{0}只股票...".format(len(codes)))

        # init progress
        self._progress.init(len(codes), 100, 5)

        for code in codes:
            if self._ticksEngine.loadCodeN(code, [self._startTicksDay, self._endTicksDay]):

                dfs = self._ticksEngine.getDataFrame(code, adj=True, continuous=self._strategy.continuousTicks)
                try:
                    self._strategy.onStockTicks(code, dfs)
                except AssertionError:
                    raise
                except Exception as ex:
                    if DyStockSelectCommon.enableSelectEngineException:
                        self._info.print('{0}[{1}]: onStockTicks异常:{2}'.format(code, self._daysEngine.stockAllCodes[code], repr(ex)), DyLogData.error)

            self._progress.update()

        self._info.print("Ticks数据运行完成")

    def _runLoop(self):
        self._info.print("开始运行选股策略: {0}".format(self._strategy.chName), DyLogData.ind)

        self._runDaysLoop()
        self._runTicksLoop()

    def _run(self):
        # load
        if not self._onLoad():
            return False

        # init strategy
        try:
            self._strategy.onInit(self._dataEngine, self._errorDataEngine)
        except Exception as ex:
            traceback.print_exc()
            self._info.print('策略onInit异常: {}'.format(ex), DyLogData.error)
            return False

        # run loop
        self._runLoop()

        # done for strategy
        self._strategy.onDone()

        # done for Engine
        self._result = self._strategy.onDoneForEngine(self._dataEngine, self._errorDataEngine)

        # to trade
        self._resultForTrade = self._strategy.toTrade()

        return True

    @property
    def result(self):
        """ 选股结果 """
        return self._result

    @property
    def resultForTrade(self):
        """ 为实盘的选股结果 """
        return self._resultForTrade

    def setTestedStocks(self, codes=None):
        self._testedStocks = codes

    def runStrategy(self, strategyCls, paramters):
        self._info.print("开始准备运行选股策略: {0}".format(strategyCls.chName), DyLogData.ind)
        self._info.initProgress()

        # init
        self._init()

        # 重载策略模块以获取最新代码（如果启用）
        if self._autoReloadStrategy:
            strategyCls = self._reloadStrategyModule(strategyCls)

        # 智能数据预检查
        if not self._smartDataPreCheck(strategyCls, paramters):
            self._eventEngine.put(DyEvent(DyEventType.fail))
            return

        # create strategy instance
        self._strategy = strategyCls(paramters, self._info)

        # run
        if self._run():
            # 保存选股结果到MongoDB
            if self._autoSaveToMongoDB and self._result and len(self._result) > 1:
                self._mongoDBSaver.saveResult(strategyCls, self._result, paramters)
                
            # ack
            event = DyEvent(DyEventType.stockSelectStrategySelectAck)
            event.data['class'] = strategyCls
            event.data['result'] = self._result
            event.data['baseDate'] = self._strategy.baseDate

            self._eventEngine.put(event)

            # finish
            self._eventEngine.put(DyEvent(DyEventType.finish))

            ret = True
        else:
            # fail
            self._eventEngine.put(DyEvent(DyEventType.fail))

            ret = False

        return ret

    @property
    def autoSaveToMongoDB(self):
        """是否自动保存选股结果到MongoDB"""
        return self._autoSaveToMongoDB
        
    @autoSaveToMongoDB.setter
    def autoSaveToMongoDB(self, value):
        """设置是否自动保存选股结果到MongoDB"""
        self._autoSaveToMongoDB = value
        
    def saveResultToMongoDB(self, strategyCls, result, param=None):
        """手动保存选股结果到MongoDB"""
        return self._mongoDBSaver.saveResult(strategyCls, result, param)
        
    def getResultFromMongoDB(self, strategyCls, selectDate=None, limit=100):
        """从MongoDB获取选股结果"""
        return self._mongoDBSaver.getResult(strategyCls, selectDate, limit)

    def _stockSelectStrategySelectReqHandler(self, event):
        # unpack
        strategyCls = event.data['class']
        paramters = event.data['param']

        self.runStrategy(strategyCls, paramters)

    def _stockSelectTestedCodesHandler(self, event):
        self._testedStocks = event.data

    def _registerEvent(self):
        self._eventEngine.register(DyEventType.stockSelectStrategySelectReq, self._stockSelectStrategySelectReqHandler, DyStockSelectEventHandType.engine)
        self._eventEngine.register(DyEventType.stockSelectTestedCodes, self._stockSelectTestedCodesHandler, DyStockSelectEventHandType.engine)
