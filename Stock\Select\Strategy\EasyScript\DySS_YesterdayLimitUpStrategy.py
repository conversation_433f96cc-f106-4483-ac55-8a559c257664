#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
昨日涨停选股策略

基于通达信公式：20天内无涨停，昨天涨停
- 条件1：昨天涨停（涨幅>=9.8%）
- 条件2：20天内只有1次涨停（即昨天的涨停）
- 适用于寻找突破性涨停的股票
"""

from datetime import datetime
from collections import OrderedDict

from .DySS_ScriptStrategyBase import DySS_ScriptStrategyBase
from DyCommon.DyCommon import DyLogData

class DySS_YesterdayLimitUpStrategy(DySS_ScriptStrategyBase):
    """
    昨日涨停选股策略
    
    策略逻辑：
    1. 昨天涨停：昨日涨幅>=9.8%
    2. 20天内无涨停：在过去20天中只有昨天1次涨停
    3. 排除ST股票和新股
    
    适用场景：
    - 寻找突破性涨停机会
    - 识别强势启动信号
    - 短线交易参考
    """
    
    name = 'DySS_YesterdayLimitUpStrategy'
    chName = '昨日涨停选股策略'

    param = OrderedDict\
                ([
                    ('基准日期', datetime.today().strftime("%Y-%m-%d")),
                    ('数据周期', 30),  # 需要至少20天数据
        
        ('指标脚本', """
# 昨日涨停选股策略
# 基于通达信公式：20天内无涨停，昨天涨停

# 涨停条件定义（涨幅>=9.8%）
ZT:=(C-REF(C,1))/REF(C,1)*100>=9.8;

# 条件1：直接判断昨天是否涨停（更简单直接）
CON1:=REF(ZT,1);

# 条件2：20天内只有1次涨停
CON2:=COUNT(ZT,20)==1;

# 附加过滤条件
# 排除价格过低的股票（<3元）
PRICE_FILTER:=C>=3;

# 排除成交量过小的股票（成交量<前5日均量）
VOL_FILTER:=V>MA(V,5);

# 最终选股条件
SELECT_STOCK:=CON1 AND CON2 AND PRICE_FILTER AND VOL_FILTER;
"""),
        
        ('选股说明', """
策略说明：
1. 涨停定义：涨幅>=9.8%（考虑四舍五入）
2. 昨日涨停：直接判断昨天是否涨停（REF(ZT,1)）
3. 20天内唯一：过去20天内只有昨天1次涨停
4. 价格过滤：股价>=3元，避免低价股
5. 成交量过滤：成交量>5日均量，确保有资金关注

适用场景：
- 突破性涨停后的跟踪
- 强势股启动信号识别
- 短线交易机会发现

风险提示：
- 涨停后可能面临回调风险
- 需要结合市场环境判断
- 建议设置止损位
"""),

                    ('风险控制', """
风险控制建议：
1. 仓位控制：单只股票不超过总资金的5%
2. 止损设置：跌破昨日最低价止损
3. 止盈设置：获利10-20%考虑减仓
4. 市场环境：在强势市场中使用效果更佳
5. 避免追高：开盘涨幅过大时谨慎介入
"""),

                    # 标准过滤参数
                    ('流通市值下限(亿)', 30),
                    ('流通市值上限(亿)', 800),
                    ('最低价格(元)', 3.0),
                    ('最大选股数量', 50),
                    ('显示中间变量', True),
                ])

    paramToolTip = {
        '数据周期': '加载的历史数据天数，至少需要20天以上',
        '指标脚本': '昨日涨停选股的核心脚本，基于通达信公式实现',
        '最低价格(元)': '过滤掉价格过低的股票，避免低价股风险',
        '显示中间变量': '是否在结果中显示脚本中的中间变量计算结果',
    }

    def __init__(self, param, info):
        super().__init__(param, info)

        # 策略特定说明
        self._info.print("昨日涨停选股策略已初始化", DyLogData.info)
        self._info.print("策略逻辑：20天内无涨停，昨天涨停", DyLogData.info)
