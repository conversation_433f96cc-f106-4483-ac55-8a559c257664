"""
测试板块数据功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from DyCommon.DyCommon import *
from Stock.Data.Engine.DyStockDataSectorManager import DyStockDataSectorManager
from Stock.Data.Engine.DyStockMongoDbEngine import DyStockMongoDbEngine


class MockInfo:
    """模拟信息输出对象"""
    def print(self, text, level=None):
        print(f"[{level}] {text}")


class MockGateway:
    """模拟数据网关"""
    def getSectorList(self, keywords=None):
        return None
    
    def getSectorConstituents(self, sector):
        return None


def test_sector_manager():
    """测试板块数据管理器"""
    print("=" * 50)
    print("测试板块数据管理器")
    print("=" * 50)
    
    # 创建模拟对象
    info = MockInfo()
    gateway = MockGateway()
    
    # 创建MongoDB引擎（使用None作为占位符）
    mongoDbEngine = None
    
    # 创建板块数据管理器
    sectorManager = DyStockDataSectorManager(mongoDbEngine, gateway, info)
    
    # 测试获取板块列表
    print("\n1. 测试获取板块列表")
    sectors = sectorManager.getSectorList()
    print(f"获取到{len(sectors)}个板块:")
    for i, sector in enumerate(sectors[:10]):  # 显示前10个
        print(f"  {i+1}. {sector}")
    if len(sectors) > 10:
        print(f"  ... 还有{len(sectors)-10}个板块")
    
    # 测试获取板块成分股
    print("\n2. 测试获取板块成分股")
    if sectors:
        test_sector = sectors[0]
        constituents = sectorManager.getSectorConstituents(test_sector)
        print(f"板块[{test_sector}]的成分股({len(constituents)}只):")
        for i, (code, name) in enumerate(constituents):
            print(f"  {i+1}. {code} {name}")
    
    # 测试关键字过滤
    print("\n3. 测试关键字过滤")
    ai_sectors = sectorManager.getSectorList(keywords=['人工智能', 'AI'])
    print(f"包含'人工智能'或'AI'的板块({len(ai_sectors)}个):")
    for sector in ai_sectors:
        print(f"  {sector}")
    
    # 测试板块分类
    print("\n4. 测试板块分类")
    categories = sectorManager.getSectorCategories()
    print("支持的板块分类:")
    for prefix, category in categories.items():
        print(f"  {prefix}: {category}")
    
    # 测试搜索功能
    print("\n5. 测试搜索功能")
    search_results = sectorManager.searchSectors('芯片')
    print(f"搜索'芯片'的结果({len(search_results)}个):")
    for sector in search_results:
        print(f"  {sector}")


def test_mock_data():
    """测试模拟数据"""
    print("=" * 50)
    print("测试模拟数据")
    print("=" * 50)
    
    info = MockInfo()
    gateway = MockGateway()
    mongoDbEngine = None
    
    sectorManager = DyStockDataSectorManager(mongoDbEngine, gateway, info)
    
    # 测试模拟板块列表
    mock_sectors = sectorManager._getMockSectorList()
    print(f"模拟板块列表({len(mock_sectors)}个):")
    for sector in mock_sectors:
        print(f"  {sector}")
    
    # 测试模拟成分股
    print(f"\n模拟成分股数据:")
    test_sectors = ['TGN人工智能', 'TGN芯片', '其他板块']
    
    for sector in test_sectors:
        constituents = sectorManager._getMockConstituents(sector)
        print(f"  {sector}: {len(constituents)}只股票")
        for code, name in constituents:
            print(f"    {code} {name}")


def test_cache_mechanism():
    """测试缓存机制"""
    print("=" * 50)
    print("测试缓存机制")
    print("=" * 50)
    
    info = MockInfo()
    gateway = MockGateway()
    mongoDbEngine = None
    
    sectorManager = DyStockDataSectorManager(mongoDbEngine, gateway, info)
    
    # 第一次获取（应该从数据源获取）
    print("第一次获取板块列表:")
    sectors1 = sectorManager.getSectorList()
    print(f"获取到{len(sectors1)}个板块")
    
    # 第二次获取（应该从缓存获取）
    print("\n第二次获取板块列表:")
    sectors2 = sectorManager.getSectorList()
    print(f"获取到{len(sectors2)}个板块")
    
    # 验证缓存是否生效
    print(f"\n缓存验证: {sectors1 == sectors2}")
    
    # 测试成分股缓存
    if sectors1:
        test_sector = sectors1[0]
        print(f"\n第一次获取板块[{test_sector}]成分股:")
        constituents1 = sectorManager.getSectorConstituents(test_sector)
        print(f"获取到{len(constituents1)}只股票")
        
        print(f"\n第二次获取板块[{test_sector}]成分股:")
        constituents2 = sectorManager.getSectorConstituents(test_sector)
        print(f"获取到{len(constituents2)}只股票")
        
        print(f"成分股缓存验证: {constituents1 == constituents2}")


def test_error_handling():
    """测试错误处理"""
    print("=" * 50)
    print("测试错误处理")
    print("=" * 50)
    
    info = MockInfo()
    gateway = MockGateway()
    mongoDbEngine = None
    
    sectorManager = DyStockDataSectorManager(mongoDbEngine, gateway, info)
    
    # 测试空板块名称
    print("测试空板块名称:")
    result = sectorManager.getSectorConstituents("")
    print(f"结果: {result}")
    
    # 测试None板块名称
    print("\n测试None板块名称:")
    result = sectorManager.getSectorConstituents(None)
    print(f"结果: {result}")
    
    # 测试不存在的板块
    print("\n测试不存在的板块:")
    result = sectorManager.getSectorConstituents("不存在的板块")
    print(f"结果: {len(result)}只股票")


if __name__ == "__main__":
    try:
        # 运行所有测试
        test_sector_manager()
        test_mock_data()
        test_cache_mechanism()
        test_error_handling()
        
        print("\n" + "=" * 50)
        print("所有测试完成")
        print("=" * 50)
        
    except Exception as ex:
        print(f"测试异常: {ex}")
        import traceback
        traceback.print_exc()
